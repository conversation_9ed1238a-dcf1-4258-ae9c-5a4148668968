package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.OMSFetchShipmentResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class PnpOrderSubmitProcessorImpl implements ShipmentOrderSubmitProcessor {

    private final PNPShipmentOutboundBrokerService pnpShipmentOutboundBrokerService;

    public PnpOrderSubmitProcessorImpl(PNPShipmentOutboundBrokerService pnpShipmentOutboundBrokerService) {
        this.pnpShipmentOutboundBrokerService = pnpShipmentOutboundBrokerService;
    }

    @Override
    public void processShipmentOrderEvent(OMSFetchShipmentResponse omsShipment, Shipment shipment) {
        log.info("Submit order to PNP");
        pnpShipmentOutboundBrokerService.submitOrderToPNP(shipment, omsShipment, false);
    }

    @Override
    public boolean handlesShipmentType(String shipmentType) {
        // TODO check on OTCHS shipment type won't work in all cases. there are otchs shipmenttype orders which goes
        //      to PNP also. we need to have this check on the fulfillment location like PNP, DC etc.
        //      it would not have created problem now but once frontstore, otchs orders comes to this flow, this will break.
        return !"OTCHS".equalsIgnoreCase(shipmentType);
    }
}