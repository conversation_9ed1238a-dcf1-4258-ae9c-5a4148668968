
package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder
public class OrderInfo {

    private String enterpriseCode;
    private String channelID;
    private String channelType;
    private String carePassIndicator;
    private String memberID;
    private String memberType;
    private String docType;
    private String entryPoint;
    private String entryType;
    private String deliveryType;
    private String deliveryMode;
    private String deliveryMethod;
    private String orderId;
    private String salesOrderNumber;
    @JsonProperty("eSig")
    private String eSig;
    private String orderSubmittedDateTime;
    private String orderPromisedPickUpTime;
    private String orderPromisedDeliveryTime;
    private String storeNbr;
    private String shipToStoreNbr;
    private double orderSubTotal;
    private String paymentIndicator;
    private String storePickUpLocation;
    private String ecCardNumber;
    private String orderType;
    private CustomerInfo customerInfo;
    private ItemInfo itemInfo;
    private String routeIdentifier;
}
