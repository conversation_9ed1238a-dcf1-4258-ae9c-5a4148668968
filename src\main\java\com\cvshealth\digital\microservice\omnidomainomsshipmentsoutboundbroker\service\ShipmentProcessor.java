package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;

import java.util.Map;


public interface ShipmentProcessor {

    void processShipment(Shipment shipment, Map<String, String> headers);

    void cancelShipment(Shipment shipment, Map<String, String> headers);

}
