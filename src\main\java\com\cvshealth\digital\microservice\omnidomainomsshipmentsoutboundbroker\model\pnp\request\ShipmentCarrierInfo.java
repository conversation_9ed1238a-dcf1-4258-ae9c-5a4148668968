
package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentCarrierInfo {

    private CarrierInfo carrierInfo;
    private List<ShipmentInfo> shipmentInfo;
    private String shipHandlingInstruction;
    private String shipMethodIndicator;

}
