name: App Deployment Non-Prod

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - '**/deploy-configs/specialty/oms-qa/**'
      - '**/deploy-configs/rxdelivery/oms-qa/**'

permissions:
  id-token: write
  contents: read
  pull-requests: write

jobs:
  detect-changes:
    runs-on:
      group: cvs-linux-self-hosted
    outputs:
      ENV_NAME: ${{ steps.set-env.outputs.ENV_NAME }}
      HELM_FILE_VERSION: ${{ steps.set-env.outputs.HELM_FILE_VERSION }}
      ADJUSTED_PATH: ${{ steps.set-env.outputs.ADJUSTED_PATH }}
      APP_NAME: ${{ steps.set-env.outputs.APP_NAME }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Detect changes
        id: changes
        uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36
        with:
          base: ${{ github.ref_name }}
          filters: |
            oms-specialty-qa_v1:
            - '**/deploy-configs/specialty/oms-qa/**'
            oms-rxdelivery-qa_v1:
            - '**/deploy-configs/rxdelivery/oms-qa/**'


      - name: Set Environment Variables
        id: set-env
        run: |

          if [ "${{ steps.changes.outputs.oms-specialty-qa_v1 }}" = "true" ]; then
            echo "ENV_NAME=oms-qa" >> $GITHUB_OUTPUT
            echo "HELM_FILE_VERSION=v1" >> $GITHUB_OUTPUT
            echo "ADJUSTED_PATH=specialty" >> $GITHUB_OUTPUT
            echo "APP_NAME=omni-domain-oms-shipments-specialty-ob" >> $GITHUB_OUTPUT
          elif [ "${{ steps.changes.outputs.oms-rxdelivery-qa_v1 }}" = "true" ]; then
            echo "ENV_NAME=oms-qa" >> $GITHUB_OUTPUT
            echo "HELM_FILE_VERSION=v1" >> $GITHUB_OUTPUT
            echo "ADJUSTED_PATH=rxdelivery" >> $GITHUB_OUTPUT
            echo "APP_NAME=omni-domain-oms-shipments-rxdelivery-ob" >> $GITHUB_OUTPUT
          fi

  deploy:
    uses: cvs-health-source-code/gitops-cd-workflow/.github/workflows/helm-deploy.yaml@v1
    needs: [detect-changes]
    secrets: inherit
    with:
      app-name: ${{ needs.detect-changes.outputs.APP_NAME }}
      environment: ${{ needs.detect-changes.outputs.ENV_NAME }}
      helm-template-repo: ${{ vars.HELM_TEMPLATE_REPO }}
      helm-file-version: ${{ needs.detect-changes.outputs.HELM_FILE_VERSION }}
      helm-template-branch: ${{ vars.HELM_TEMPLATE_BRANCH }}
      canary-weight: "0"
      adjusted-tenant-path:  ${{ needs.detect-changes.outputs.ADJUSTED_PATH }}
      docker-image-tag: 1.0.28
