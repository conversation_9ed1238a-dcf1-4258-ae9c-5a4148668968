package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util;

import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.framework.service.logging.utils.json.ZonedLocalTimeSerializer;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.common.JacksonBeanPostProcessor;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.common.StringTrimModule;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;


public final class CVSCommonUtil {

    private static final String YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";
    private static final ObjectMapper objectMapper;

    static {
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(ZonedDateTime.class, new ZonedLocalTimeSerializer(DateTimeFormatter.ISO_INSTANT));
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ISO_DATE));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ISO_DATE));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ISO_TIME));
        objectMapper = JsonMapper.builder()
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                .addModules(new StringTrimModule(), javaTimeModule)
                .build();

    }

    private CVSCommonUtil() {
    }

    public static String toJSONString(Object obj) {

        String jsonString = "";

        try {
            jsonString = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (JsonProcessingException jsonProcessingException) {
            CvsLogger.error("Error while converting object to JSON: {}", jsonProcessingException);
        }

        return jsonString;
    }

    public static String convertDateTime(String strDateTimeFrom) {
        String strDateTimeTo = removeOffset(strDateTimeFrom, '.');
        strDateTimeTo = strDateTimeTo + "-00:00";
        return strDateTimeTo;
    }

    public static String removeOffset(String str, char delimiter) {
        int lastIndex = str.lastIndexOf(delimiter);

        if (lastIndex != -1) {
            return str.substring(0, lastIndex);
        } else {
            if (str.endsWith("Z")) {
                str = str.substring(0, str.length() - 1);
            }
            return str;
        }
    }

    public static String currentDareTime(){
        DateTimeFormatter format = DateTimeFormatter.ofPattern(YYYY_MM_DD_T_HH_MM_SS);
        return format.format(LocalDateTime.now());
    }

    public static String generateTypeUUID() throws NoSuchAlgorithmException {
        try {

            String standardChars ="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

            byte[] bytes = standardChars.getBytes(StandardCharsets.UTF_8);
            MessageDigest md = MessageDigest.getInstance("SHA-1");

            byte[] hash = md.digest(bytes);

            long msb = getLeastAndMostSignificantBitsVersion5(hash, 0);
            long lsb = getLeastAndMostSignificantBitsVersion5(hash, 8);
            msb &= ~(0xfL << 12);
            msb |= 5L << 12;
            lsb &= ~(0x3L << 62);
            lsb |= 2L << 62;
            return new UUID(msb, lsb).toString();

        } catch (NoSuchAlgorithmException e) {
            throw new AssertionError(e);
        }
    }

    private static long getLeastAndMostSignificantBitsVersion5(final byte[] src, final int offset) {
        long ans = 0;
        for (int i = offset + 7; i >= offset; i -= 1) {
            ans <<= 8;
            ans |= src[i] & 0xffL;
        }
        return ans;
    }
}
