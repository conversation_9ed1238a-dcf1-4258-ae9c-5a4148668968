package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ShipmentEventSource {

    SPOMS("SPOMS", "1", "SPOMS"),
    PPS("PPS", "2", "PPS Sanitas"),
    SCANDATA("SCANDATA", "3", "Scan data"),
    DASH("DASH", "4", "Dash"),
    EXTERNAL("EXTERNAL", "5", "340B CE External entity"),
    INTERNAL("INTERNAL", "6", "Outbound broker service");

    private final String eventName;
    private final String eventCode;
    private final String eventDescription;

    private static final Map<String, ShipmentEventSource> shipmentEventSourceMap = new HashMap<>();

    static {
        for (ShipmentEventSource e : ShipmentEventSource.values()) {
            if (shipmentEventSourceMap.put(e.getEventCode(), e) != null) {
                throw new IllegalArgumentException("invalid event code: " + e.getEventCode());
            }
        }
    }

    public static ShipmentEventSource getEvent(String code) {
        return shipmentEventSourceMap.get(code);
    }

}

