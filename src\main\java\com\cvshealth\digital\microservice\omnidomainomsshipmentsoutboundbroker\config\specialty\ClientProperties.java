package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Data
@ConfigurationProperties(prefix = "clients")
@Profile("specialty")
public class ClientProperties {
    private Map<String, ClientConfig> configs;
}
