package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery;


import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.oms.*;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.request.OrderInfo;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.CVSCommonUtil;



import java.security.NoSuchAlgorithmException;

public class OMSMapper {

    public DeliveryInputMessage mapDeliveryInputMessage(OrderInfo orderInfo) throws NoSuchAlgorithmException {
        DeliveryInputMessage deliveryInputMessage = new DeliveryInputMessage();
        String currentDateTime = CVSCommonUtil.currentDareTime();
        String strUID = CVSCommonUtil.generateTypeUUID();
        if (null != orderInfo && null != orderInfo.getOrderId()) {
            RequestMetaData requestMetaData = new RequestMetaData();
            requestMetaData.setConversationID(strUID);
            deliveryInputMessage.setRequestMetaData(requestMetaData);

            RequestPayloadData requestPayloadData = new RequestPayloadData();
            mapPayloadData(requestPayloadData);
            mapAdditionalData(requestPayloadData, orderInfo, currentDateTime);
            deliveryInputMessage.setRequestPayloadData(requestPayloadData);
        }
        return deliveryInputMessage;
    }

    private void mapPayloadData(RequestPayloadData requestPayloadData){
        PayLoadData data = new PayLoadData();
        data.setEventName("UPDATE");
        data.setTimeZone("Eastern Standard Time");
        requestPayloadData.setData(data);
    }

    private void mapAdditionalData(RequestPayloadData requestPayloadData, OrderInfo orderInfo, String currentDateTime){

        AdditionalData additionalData = new AdditionalData();
        additionalData.setOrderID(orderInfo.getOrderId());
        additionalData.setStoreNumber(orderInfo.getStoreNbr());
        additionalData.setDeliveryMode("SHIPMENT_SERVICE");
        additionalData.setDeliveryCode(orderInfo.getDeliveryMethod());
        additionalData.setEventTimestamp(currentDateTime);
        additionalData.setInterfaceType("MPNP");
        requestPayloadData.setAdditionalData(additionalData);

    }
}
