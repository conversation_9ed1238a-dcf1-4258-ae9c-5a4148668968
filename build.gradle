plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.5'
    id "io.freefair.lombok" version "8.6"
    id 'maven-publish'
    id 'jacoco'
}
java { 
    sourceCompatibility = java_version
    withSourcesJar()
}
configurations {
    annotationProcessor {
        extendsFrom implementation
    }
    developmentOnly {
        extendsFrom implementation
    }
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenLocal()
    maven {
        name = artifactory_repo
        url = artifactory_url
       //credentials(PasswordCredentials)
        credentials {
			username = System.getenv("ARTIFACTORY_USERNAME")
            password = System.getenv("ARTIFACTORY_TOKEN")
		}
    }
}
dependencies {
    implementation  "com.cvs.oms.common:oms-common-lib:0.0.1-20240409.191053-1"
    // Platform BOM
    implementation(platform("com.cvshealth.digital.framework:digital-spring-boot-parent:${digital_spring_boot_framework_version}"))
    annotationProcessor platform("com.cvshealth.digital.framework:digital-spring-boot-parent:${digital_spring_boot_framework_version}")

    // CVS Dependencies
    implementation "com.cvshealth.digital.framework:digital-spring-boot-starter-web:${digital_spring_boot_framework_version}"
    implementation "com.cvshealth.digital.framework:digital-service-kafka:${digital_spring_boot_framework_version}"
    implementation "io.github.resilience4j:resilience4j-spring-boot3:2.2.0"

    implementation "com.ibm.mq:mq-jms-spring-boot-starter:3.4.3"
    implementation "org.apache.httpcomponents.client5:httpclient5:5.4.2"

    // spring boot test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    // junit
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

    // mockito
    testImplementation 'org.mockito:mockito-core'

    //jbehave
    testImplementation("org.jbehave:jbehave-spring:${jbehave_version}")
    testImplementation("org.jbehave:jbehave-gherkin:${jbehave_version}")
}

tasks.named('test') {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}

tasks.named("bootJar") {	
    archiveFileName = "${rootProject.name}-exec.jar"
}

publishing {
    publications {
        "${name}"(MavenPublication) {
            artifactId = name
            from components.java
        }
    }
}

test {
    finalizedBy jacocoTestReport // report is always generated after tests run
}
test {
    testLogging
            {
                afterSuite
                        {
                            desc, result ->
                                if (result.testCount == 0)
                                    throw new GradleException("No tests were executed. Build failed.")
                        }
            }
}
jacocoTestReport {
    dependsOn test // tests are required to run before generating the report
    reports {
        xml.required = true
    }
}