package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.ShipmentEvent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentEventHs {
    private long shipmentEventId;
    private long shipmentId;
    private long shipmentItemId;
    private int eventCd;
    private ShipmentEvent event;
    private int eventStatusCd;
    private Instant eventTs;
    private String sourceSystemCd;
    private int serviceCd;
    private long parentEventId;
    private ShipmentStatusHs shipmentStatusHs;
}
