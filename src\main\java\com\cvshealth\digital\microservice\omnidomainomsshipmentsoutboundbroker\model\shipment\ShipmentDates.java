package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentDates {

    private String pickUpWindowFromDate;
    private String pickUpWindowToDate;
    private String deliveryWindowFromDate;
    private String deliveryWindowToDate;

}
