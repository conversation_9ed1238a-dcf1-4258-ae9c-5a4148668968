package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.DatabaseProgId;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.ShipmentStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Shipment {

    private long shipmentId;
    private Instant shipDate;
    @NonNull
    private String shipNode;
    private String shipNodeType;
    private String scac;
    private String carrierServiceCode;
    private ShipmentStatus shipmentStatus;
    private Double totalWeight;
    private Double totalVolume;
    private int totalQuantity;
    private Instant deliveryTime;
    private String deliveryCode;
    @NonNull
    private String shipmentNo;
    private String organizationId;
    private Instant storeSlaDate;
    private Instant customerSlaDate;
    private String fulfillmentChannel;
    private Instant statusUpdateDate;
    @NonNull
    private String shipmentType;
    private boolean isHazardous;
    private boolean isShipmentComplete;
    private boolean isDeliveryComplete;
    @NonNull
    private String orderNo;
    private String deliveryMethod;
    private String orderType;
    private String levelOfService;
    private String posTransactionNo;
    private String shipmentCancellationReason;
    private boolean isReshipShipment;
    private String deliveryActivityCode;
    private boolean delayNotificationEmail;
    private int numOfBags;
    private int numOfBoxes;
    private String rxLockerPin;
    private String carrierServiceName;
    private String omsProcessingTimeToStore;
    private String omsProcessingDelay;
    private String courierFailureReasonCode;
    private int courierRetries;
    private boolean isShipmentAcknowledged;
    private Double bagFeeAmount;
    private String deliveryId;
    private Instant creationTimestamp;
    private Instant lastModifiedTimestamp;
    private DatabaseProgId createProgId;
    private DatabaseProgId modifyProgId;
    private String orderChannel;
    private boolean isCarepassOrder;
    private String customerProfileId;
    private String customerProfileType;
    private String extraCareCardNo;
    private ShipmentExtn shipmentExtn;
    private Set<ShipmentItem> shipmentItemList;
    private Set<ShipmentAddress> shipmentAddressList;
    private Instant orderDate;
    private String shipNodeLocale;
    private String storePickupLocation;
    private Set<ShipmentContainer> shipmentContainerList;
    private String entryType;
    private ShipmentDates shipmentDates;

    private String preferredCarrier;
    private String carrierShipCd;
    private String carrierServiceDescription;
    private String carrierShopCd;
    private String companyName;
    private String individualName;
    private String billingAccountId;
    private boolean isColdpackIn;
    private boolean isUspsProhibitedIn;
    private String shipFromLocationCd;
    private String fillFromLocationCd;
    private String fillFromLocationDea;
    private String distributionCenter;
    private Clients clients;
    private Attributes attributes;
    private List<String> excludeCarriers;
    private List<String> shippingOptions;
    private ShipmentPrints shipmentPrints;
    private Set<ShipmentEventHs> shipmentEventHsList;
}
