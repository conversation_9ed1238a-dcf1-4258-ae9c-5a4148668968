package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.HeaderValidator;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.specialty.SpecialtyHeaderValidator;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.producer.EventPublisherService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentProcessor;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.impl.SpecialtyShipmentProcessorImpl;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.specialty.ClientService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.ShipmentUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.client.RestClient;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;


@ExtendWith(MockitoExtension.class)
class SpecialtyConfigTest {

    @Mock
    private RestClient restClient;

    @Mock
    private ExternalEntityRestClientCache externalEntityRestClientCache;

    @Mock
    private ClientService clientService;

    @Mock
    private EventPublisherService eventPublisherService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private ClientProperties clientProperties;

    @Mock
    private ShipmentUtil shipmentUtil;

    private SpecialtyConfig specialtyConfig;

    @BeforeEach
    void setUp() {
        specialtyConfig = new SpecialtyConfig(restClient, externalEntityRestClientCache);
    }

    @Test
    void testConstructor() {
        // Act & Assert
        assertNotNull(specialtyConfig);
        // We can't directly access private fields, but we can verify the constructor doesn't throw
        assertDoesNotThrow(() -> new SpecialtyConfig(restClient, externalEntityRestClientCache));
    }

    @Test
    void testConstructorWithNullRestClient() {
        // Act & Assert
        assertDoesNotThrow(() -> new SpecialtyConfig(null, externalEntityRestClientCache));
    }

    @Test
    void testConstructorWithNullExternalEntityRestClientCache() {
        // Act & Assert
        assertDoesNotThrow(() -> new SpecialtyConfig(restClient, null));
    }

    @Test
    void testConstructorWithAllNullParameters() {
        // Act & Assert
        assertDoesNotThrow(() -> new SpecialtyConfig(null, null));
    }

    @Test
    void testGetShipmentProcessor() {
        // Act
        ShipmentProcessor processor = specialtyConfig.getShipmentProcessor(clientService, objectMapper);

        // Assert
        assertNotNull(processor);
        assertInstanceOf(SpecialtyShipmentProcessorImpl.class, processor);
    }

    @Test
    void testGetShipmentProcessorWithNullClientService() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            ShipmentProcessor processor = specialtyConfig.getShipmentProcessor(null, objectMapper);
            assertNotNull(processor);
        });
    }

    @Test
    void testGetShipmentProcessorReturnsDifferentInstances() {
        // Act
        ShipmentProcessor processor1 = specialtyConfig.getShipmentProcessor(clientService, objectMapper);
        ShipmentProcessor processor2 = specialtyConfig.getShipmentProcessor(clientService, objectMapper);

        // Assert
        assertNotNull(processor1);
        assertNotNull(processor2);
        assertNotSame(processor1, processor2, "Should return different instances each time");
    }

    @Test
    void testGetClientService() {
        // Act
        ClientService service = specialtyConfig.getClientService(clientProperties, eventPublisherService, objectMapper);

        // Assert
        assertNotNull(service);
        assertInstanceOf(ClientService.class, service);
    }

    @Test
    void testGetClientServiceWithNullClientProperties() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            ClientService service = specialtyConfig.getClientService(null, eventPublisherService, objectMapper);
            assertNotNull(service);
        });
    }

    @Test
    void testGetClientServiceWithNullEventPublisherService() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            ClientService service = specialtyConfig.getClientService(clientProperties, null, objectMapper);
            assertNotNull(service);
        });
    }

    @Test
    void testGetClientServiceWithAllNullParameters() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            ClientService service = specialtyConfig.getClientService(null, null, null);
            assertNotNull(service);
        });
    }

    @Test
    void testGetClientServiceReturnsDifferentInstances() {
        // Act
        ClientService service1 = specialtyConfig.getClientService(clientProperties, eventPublisherService, objectMapper);
        ClientService service2 = specialtyConfig.getClientService(clientProperties, eventPublisherService, objectMapper);

        // Assert
        assertNotNull(service1);
        assertNotNull(service2);
        assertNotSame(service1, service2, "Should return different instances each time");
    }

    @Test
    void testGetHeaderValidator() {
        // Act
        HeaderValidator validator = specialtyConfig.getHeaderValidator(shipmentUtil);

        // Assert
        assertNotNull(validator);
        assertInstanceOf(SpecialtyHeaderValidator.class, validator);
    }

    @Test
    void testConfigurationClassAnnotations() {
        // Act
        Class<SpecialtyConfig> configClass = SpecialtyConfig.class;

        // Assert
        assertTrue(configClass.isAnnotationPresent(Configuration.class), "Should have @Configuration annotation");
        assertTrue(configClass.isAnnotationPresent(Profile.class), "Should have @Profile annotation");
        
        Profile profileAnnotation = configClass.getAnnotation(Profile.class);
        assertEquals("specialty", profileAnnotation.value()[0], "Should have 'specialty' profile");
    }

    @Test
    void testBeanMethodAnnotations() throws NoSuchMethodException {
        // Arrange
        Class<SpecialtyConfig> configClass = SpecialtyConfig.class;

        // Act & Assert
        Method getShipmentProcessorMethod = configClass.getMethod("getShipmentProcessor", ClientService.class, ObjectMapper.class);
        assertTrue(getShipmentProcessorMethod.isAnnotationPresent(org.springframework.context.annotation.Bean.class), 
                   "getShipmentProcessor should have @Bean annotation");

        Method getClientServiceMethod = configClass.getMethod("getClientService", ClientProperties.class, EventPublisherService.class, ObjectMapper.class);
        assertTrue(getClientServiceMethod.isAnnotationPresent(org.springframework.context.annotation.Bean.class), 
                   "getClientService should have @Bean annotation");

        Method getHeaderValidatorMethod = configClass.getMethod("getHeaderValidator", ShipmentUtil.class);
        assertTrue(getHeaderValidatorMethod.isAnnotationPresent(org.springframework.context.annotation.Bean.class), 
                   "getHeaderValidator should have @Bean annotation");
    }

    @Test
    void testBeanCreationWithRealDependencies() {
        // Arrange
        RestClient realRestClient = RestClient.builder().build();
        ExternalEntityRestClientCache realCache = new ExternalEntityRestClientCache();
        ObjectMapper realObjectMapper = new ObjectMapper();
        ClientProperties realClientProperties = new ClientProperties();
        ShipmentUtil realShipmentUtil = new ShipmentUtil();

        // Act
        SpecialtyConfig realConfig = new SpecialtyConfig(realRestClient, realCache);
        
        // Assert
        assertDoesNotThrow(() -> {
            ShipmentProcessor processor = realConfig.getShipmentProcessor(clientService, realObjectMapper);
            assertNotNull(processor);
        });

        assertDoesNotThrow(() -> {
            ClientService service = realConfig.getClientService(realClientProperties, eventPublisherService, realObjectMapper);
            assertNotNull(service);
        });

        assertDoesNotThrow(() -> {
            HeaderValidator validator = realConfig.getHeaderValidator(realShipmentUtil);
            assertNotNull(validator);
        });
    }

    @Test
    void testIntegrationWithMockedDependencies() {
        // Act
        ShipmentProcessor processor = specialtyConfig.getShipmentProcessor(clientService, objectMapper);
        ClientService service = specialtyConfig.getClientService(clientProperties, eventPublisherService, objectMapper);
        HeaderValidator validator = specialtyConfig.getHeaderValidator(shipmentUtil);

        // Assert
        assertNotNull(processor);
        assertNotNull(service);
        assertNotNull(validator);

        // Verify that the beans are created successfully with mocked dependencies
        assertInstanceOf(SpecialtyShipmentProcessorImpl.class, processor);
        assertInstanceOf(ClientService.class, service);
        assertInstanceOf(SpecialtyHeaderValidator.class, validator);
    }

    @Test
    void testMultipleBeanCreations() {
        // Act
        ShipmentProcessor processor1 = specialtyConfig.getShipmentProcessor(clientService, objectMapper);
        ShipmentProcessor processor2 = specialtyConfig.getShipmentProcessor(clientService, objectMapper);
        
        ClientService service1 = specialtyConfig.getClientService(clientProperties, eventPublisherService, objectMapper);
        ClientService service2 = specialtyConfig.getClientService(clientProperties, eventPublisherService, objectMapper);

        // Assert
        assertNotSame(processor1, processor2, "Each call should create a new ShipmentProcessor instance");
        assertNotSame(service1, service2, "Each call should create a new ClientService instance");
    }

    @Test
    void testBeanTypesAreCorrect() {
        // Act
        ShipmentProcessor processor = specialtyConfig.getShipmentProcessor(clientService, objectMapper);
        ClientService service = specialtyConfig.getClientService(clientProperties, eventPublisherService, objectMapper);
        HeaderValidator validator = specialtyConfig.getHeaderValidator(shipmentUtil);

        // Assert
        assertTrue(processor instanceof SpecialtyShipmentProcessorImpl, 
                   "Should return SpecialtyShipmentProcessorImpl instance");
        assertTrue(service instanceof ClientService, 
                   "Should return ClientService instance");
        assertTrue(validator instanceof SpecialtyHeaderValidator, 
                   "Should return SpecialtyHeaderValidator instance");
    }
}
