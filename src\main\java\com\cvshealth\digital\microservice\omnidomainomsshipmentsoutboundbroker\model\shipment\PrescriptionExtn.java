package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PrescriptionExtn {
    private Double refillsLeft;
    private String substitutedFor;
    private String substitutedForVerbiage;
    private Instant discardDate;
    private String instructions;
    private boolean isAdditionalInstructionsIncluded;
    private String auxMessage;
    private String rxLabelLanguageCode;
    private String rphInitials;
    private int daysSupply;
    private String prescriberFirstName;
    private String prescriberMiddleInitials;
    private String prescriberLastName;
    private String prescriberType;
    private String prescriberDEA;
    private String prescriberDEASuffix;
    private String prescriberPhone;
}
