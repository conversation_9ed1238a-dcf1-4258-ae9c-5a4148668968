package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery;

import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.framework.service.logging.service.LogServiceContext;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.ShipmentDetails;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.ShipmentRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.CVSCommonUtil;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.MQUtil;
import org.springframework.jms.core.JmsTemplate;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.OMSFetchShipmentResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.CVSConstants;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import static com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.CVSConstants.*;


@Slf4j
public class ShipmentService {

    private final ShipmentMapper mapper;
    private final ErrorReprocessorService errorReprocessorService;
    private final JmsTemplate jmsTemplate;

    @Value("${ibm.mq.queueKcdc}")
    private String kcdcQueueName;

    @Value("${ibm.mq.queueOrdc}")
    private String ordcQueueName;

    @Value("${ibm.mq.queueMadc}")
    private String madcQueueName;

    @Value("${ibm.mq.queueBvdc}")
    private String bvdcQueueName;

    @Value("${ibm.mq.channel}")
    private String channel;

    @Value("${ibm.mq.queueManager}")
    private String queueManager;

    @Value("${ibm.mq.connName}")
    private String connName;

    public ShipmentService(ShipmentMapper mapper, ErrorReprocessorService errorReprocessorService, JmsTemplate jmsTemplate) {
        this.mapper = mapper;
        this.errorReprocessorService = errorReprocessorService;
        this.jmsTemplate = jmsTemplate;
    }


    public String publishMessageTODc(OMSFetchShipmentResponse response)  {
        log.info("Publishing message to DC");
        String XmlToQueue=null;
        String queueName = null;
        try {
            XmlToQueue = mapper.convertShipmentToDcMessage(response);

            log.info("XML to send to queue:{}", XmlToQueue);

            if (CVSConstants.SHIPMENT_NODE_KCDC.equalsIgnoreCase(response.getShipmentDetails().getShipNode())) {
                queueName = kcdcQueueName;
                jmsTemplate.convertAndSend(kcdcQueueName, XmlToQueue);
                log.info("Message published to KCDC");
            } else if (SHIPMENT_NODE_ORDC.equalsIgnoreCase(response.getShipmentDetails().getShipNode())) {
                queueName = ordcQueueName;
                jmsTemplate.convertAndSend(ordcQueueName, XmlToQueue);
                log.info("Message published to ORDC");
            } else if (SHIPMENT_NODE_MADC.equalsIgnoreCase(response.getShipmentDetails().getShipNode())) {
                queueName = madcQueueName;
                jmsTemplate.convertAndSend(madcQueueName, XmlToQueue);
                log.info("Message published to MADC");
            } else if (SHIPMENT_NODE_BVDC.equalsIgnoreCase(response.getShipmentDetails().getShipNode())) {
                queueName = bvdcQueueName;
                jmsTemplate.convertAndSend(bvdcQueueName, XmlToQueue);
                log.info("Message published to BVDC");
            }

        } catch (Exception e) {
            log.error("Publishing to Queue: {} FAILED with error: {}", queueName, e.getMessage());
            if (queueName != null && !MQUtil.isQueueAvailable(connName, channel, queueManager, queueName)) {
                triggerErrorReprocessorFlow(response.getShipmentDetails(), "Queue is not available", "500");
            }
            LogServiceContext.addTags(TAG_ERROR_SERVERITY, ERROR_SERVERITY_CRITICAL);
            CvsLogger.error("Error while processing DC Shipment order", e);
            triggerErrorReprocessorFlow(response.getShipmentDetails(), e.getMessage(), "500");
        }

        return XmlToQueue;
    }

    private void triggerErrorReprocessorFlow(ShipmentDetails details, String errorMessage, String errorCode) {

        log.info("Calling Error Reprocessor service");

        ShipmentRequest shipment = new ShipmentRequest(details.getShipmentId(),details.getOrderChannel() );
        errorReprocessorService.reprocessFailedFlow(CVSCommonUtil.toJSONString(shipment), shipment.getShipmentId(), errorMessage, errorCode, "dcOrderSubmit");
    }

}





