# --------------------
# Application Configuration
# Prefix: app.*
# --------------------
app:
  events:
    shipmentInboundEvent:
      enabled: true
      topic: digitalomni-oms-rxdelivery-shipment-status-inbound-broker-event

# --------------------
# Spring Boot Framework / Service Configuration
# Prefix: service.*
# --------------------
service:
  context-path: /microservices/omni-domain-oms-shipments-rxdelivery-outbound-broker
  encrypt:
    method: JASYPT
    jasypt:
      key: ${JASYPT_KEY}
  logging:
    enabled: true
    #   Supported mode: CVSEVENT, LOGAPP
    mode: CVSEVENT, LOGAPP
    #   Supported destinations: CONSOLE, HTTP
    #   For HTTP, set log-app.url
    destination:
      - CONSOLE
    log-app:
      url: ""
    #   List of events to ignore while logging. Valid: ENTRY, EXIT, INFO, EX<PERSON>
    ignore-events:
    excluded-endpoints:
      - "/actuator*/**"
      - "/swagger*/**"
      - "/health*/**"
      - "/v3/api*/**"
      - "/metrics/**"
      - "/microservices/omni-domain-oms-shipments-rxdelivery-outbound-broker/actuator*/**"
      - "/microservices/omni-domain-oms-shipments-rxdelivery-outbound-broker/swagger*/**"
      - "/microservices/omni-domain-oms-shipments-rxdelivery-outbound-broker/health*/**"


  # --------------------
  # Kafka client configuration
  # Prefix: service.kafka.*
  # For more details, refer: https://cvsdigital.atlassian.net/wiki/spaces/SBREF/pages/**********/digital-service-kafka
  # --------------------
  kafka:
    producers:
      default:
        enabled: false
        client-id: omni-domain-oms-shipments-outbound-broker-client
        bootstrap-servers: lkc-knmpxm-pd0d09.us-east4.gcp.glb.confluent.cloud:9092
        properties:
          security:
            protocol: SASL_SSL
          ssl:
            endpoint:
              identification:
                algorithm: ""
          sasl:
            mechanism: PLAIN
            jaas:
              config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_CONSUMER_RXDELIVERY_KEY}" password="${KAFKA_CONSUMER_RXDELIVERY_PWD}";
    consumers:
      outbound-event:
        enabled: true
        event-type: outbound-event
        consumer-class: com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.ShipmentConsumer
        client-id: omni-domain-oms-shipments-service
        group-id: digitalomni-oms-rxdelivery-outbound-broker-listener-1
        bootstrap-servers: lkc-knmpxm-pd0d09.us-east4.gcp.glb.confluent.cloud:9092
        topics: digitalomni-oms-rxdelivery-shipment-outbound-broker-event
        commit-style: AT_LEAST_ONCE_HIGH_THROUGHPUT
        max-poll-interval: 5m
        max-poll-records: 50
        auto-offset-reset: latest
        consumer-concurrency: 1
        consumer-record-processor-concurrency: 1
        initial-delay: 5s
        properties:
          security:
            protocol: SASL_SSL
          ssl:
            endpoint:
              identification:
                algorithm: ""
          sasl:
            mechanism: PLAIN
            jaas:
              config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_CONSUMER_RXDELIVERY_KEY}" password="${KAFKA_CONSUMER_RXDELIVERY_PWD}";

shipment:
  get-by-id-url: https://internal-non-prod-qa.gke-oms-non-prod.cvshealth.com/microservices/omni-domain-oms-shipments-rxdelivery-service/v1/shipments


pnp:
  url: https://t-storeNbr-app.stores.cvs.com/service/digitalgw/bopus/orders/v2
  trust-store: ${STORE_CERTIFICATE}
  trust-store-password: ${STORE_CERTIFICATE_PASSWORD}
  timeout: 5

dma:
  url: https://internal-qa-ohs-ms.cvshealth.com/delivery/publish/v1/pnp/event

# --------------------
# REST Service configuration
# To use RestService, set rest.enabled=true and provide endpoints
# Prefix: rest.*
# --------------------
rest:
  enabled: false
  endpoint:
    service-name:
      operation-name:
        url: ""
        method: POST
        headers:
          accept: "application/json"
          content-type: "application/json"


# --------------------
# Server Configuration
# Prefix: server.*
# --------------------
server:
  port: 21000
  max-http-request-header-size: 80KB

# --------------------
# Spring Configuration including datasource
# Prefix: spring.*
# --------------------
spring:
  application:
    name: omni-domain-oms-shipments-rxdelivery-outbound-broker
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  main:
    banner-mode: off
  jpa:
    open-in-view: false
  threads.virtual.enabled: true


# --------------------
# Api Information
# Prefix: info.*
# --------------------
info:
  app:
    name: omni-domain-oms-shipments-rxdelivery-outbound-broker
    description: omni-domain-oms-shipment-outbound-broker spring boot microservice
    version: 1.0.0-SANPSHOT
    encoding: UTF-8
    java:
      source: 21
      target: 21

# --------------------
# Swagger configuration
# Prefix: springdoc.*
# --------------------
springdoc:
  packagesToScan: com.cvshealth
  pathsToMatch: /microservices/omni-domain-oms-shipments-rxdelivery-outbound-broker/**
  swagger-ui:
    enabled: true
    path: /microservices/omni-domain-oms-shipments-rxdelivery-outbound-broker/swagger/swagger-ui.html
  api-docs:
    enabled: true
    path: /microservices/omni-domain-oms-shipments-rxdelivery-outbound-broker/swagger/api-docs

# --------------------
# Actuator monitoring
# Prefix: management.*
# --------------------
management:
  security:
    enabled: false
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true
    health:
      show-details: always
    httptrace:
      excluded-endpoints: /favicon.ico,/actuator/**,/metrics/**,/microservices/omni-domain-oms-shipment-service/actuator/**
  endpoints:
    web:
      base-path: /microservices/omni-domain-oms-shipments-rxdelivery-outbound-broker/actuator
      exposure:
        include:
          - health
          - prometheus
  metrics:
    export:
      prometheus:
        enabled: true
    percentiles-histogram:
      http:
        server:
          requests: true
    distribution:
      sla:
        http:
          server:
            requests: 50ms


resilience4j:
  retry:
    instances:
      submit-order-external-service-retry:
        max-attempts: 3
        wait-duration: 500ms
        retry-exceptions:
          - java.lang.RuntimeException
      cancel-order-external-service-retry:
        max-attempts: 3
        wait-duration: 500ms
        retry-exceptions:
          - java.lang.RuntimeException
      fetch-shipment-details:
        max-attempts: 3
        wait-duration: 500ms
        retry-exceptions:
          - java.lang.RuntimeException
      dma-service:
        max-attempts: 3
        wait-duration: 500ms
        retry-exceptions:
          - java.lang.RuntimeException
      pnp-service:
        max-attempts: 3
        wait-duration: 500ms
        retry-exceptions:
          - java.lang.RuntimeException
  circuitbreaker:
    instances:
      submit-order-external-service-circuit-breaker:
        sliding-window-type: COUNT_BASED
        sliding-window-size: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 10s
        permitted-number-of-calls-in-half-open-state: 2
        automatic-transition-from-open-to-half-open-enabled: true
      cancel-order-external-service-circuit-breaker:
        sliding-window-type: COUNT_BASED
        sliding-window-size: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 10s
        permitted-number-of-calls-in-half-open-state: 2
        automatic-transition-from-open-to-half-open-enabled: true

ibm:
  mq:
    channel: CDC_JAVA_CHANNEL_01
    queueManager: RTL_A_ZAU1001
    connName: rri2eslaua1.cvs.com(30401)
    queueKcdc: FULKANREQUEST
    queueOrdc: OTCREQUEST
    queueMadc: FULFREREQUEST
    queueBvdc: FULPHXREQUEST