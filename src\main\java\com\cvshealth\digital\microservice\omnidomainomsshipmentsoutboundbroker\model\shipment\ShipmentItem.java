package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.DatabaseProgId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.Instant;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentItem {

    private long shipmentItemId;
    private long shipmentId;
    private int shipmentItemNo;
    private int orderLineNo;
    private String itemId;
    private Double netWeight;
    private int orderedQuantity; // original qty - we should not update this because we want to keep original picture
    private int quantity;  // fulfillment qty
    private int shortedQty; // what was shorted from the original item
    private String itemDesc;
    private boolean isPickable;
    private boolean isHazmat;
    private String lineCancellationReason;
    private Instant creationTimestamp;
    private Instant lastModifiedTimestamp;
    private DatabaseProgId createProgId;
    private DatabaseProgId modifyProgId;
    private Double lineTotalWithoutTax;
    private Double unitPrice;
    private Double originalUnitPrice;
    private String fillNumber;
    private String fillSeqNum;
    private String fillVerNum;
    private String rxNumber;
    private String rxStatus;
    private String itemType;
    private boolean isSubstitutionAllowed;
    private String upcNumber;
    private String alternateUPCList;
    private String substitutionItemList;

    private ShipmentItemExtn shipmentItemExtn; // stored as jsonb in the database

    private boolean isFSAItem;
    private String shipToStoreNo;
    private Double unitWeight;

    private long orderId = -1;
    private String orderNb = "";
    private long orderItemId = -1;
    private LocalDate needsDt;
    private String needsOnByCd;
    private boolean isPrescriptionDrugIn = false;
    private String ndcNumber;
    private String lotNb;
    private LocalDate lotExpiryDate;
    private Double allocatedQuantity;
    private int inventoryReservationCd;
    private String unitOfMeasure;
    private String itemForm;
    private String genericName;
    private String manufacturerName;
    private long childOrderNb;
    private String patientSourceId;
    private PrescriptionExtn prescriptionExtn;

}
