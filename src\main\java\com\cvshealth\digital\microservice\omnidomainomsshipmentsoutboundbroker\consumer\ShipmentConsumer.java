package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer;

import com.cvshealth.digital.framework.service.kafka.consumer.DigitalKafkaConsumerRecordProcessor;
import com.cvshealth.digital.framework.service.kafka.exception.KafkaConsumerException;
import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.framework.service.logging.service.LogServiceContext;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.HeaderConstants;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.ShipmentEvent;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentDetailsService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentProcessor;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.ShipmentUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.CVSConstants.ERROR_SERVERITY_CRITICAL;
import static com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.CVSConstants.TAG_ERROR_SERVERITY;

@Component
@Slf4j
@RequiredArgsConstructor
public class ShipmentConsumer implements DigitalKafkaConsumerRecordProcessor<String, byte[]>{

    private final ShipmentDetailsService shipmentDetailsService;
    private final ShipmentProcessor shipmentProcessor;
    private final ShipmentUtil shipmentUtil;
    private final HeaderValidator headerValidator;

    @Override
    public Integer onMessage(String eventType, ConsumerRecord<String, byte[]> consumerRecord) throws KafkaConsumerException {

        CvsLogger.entry("Shipment Outbound broker Entry");

        try {
            Map<String, String> headers = headerValidator.validateAndGetHeaderMap(consumerRecord);
            processShipmentRequest(headers);
        } catch (Exception e) {
            LogServiceContext.addTags(TAG_ERROR_SERVERITY, ERROR_SERVERITY_CRITICAL);
            CvsLogger.error("Error consuming shipment event", e);
            throw new KafkaConsumerException(e.getMessage(), e.getCause());
        }

        CvsLogger.exit("Shipment Outbound broker exit");

        return 0;
    }

    // This method processes the shipment request based on the event type and shipment ID.
    private void processShipmentRequest(Map<String, String> headers) {

        //get event type
        String shipmentId = shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID);
        String event = shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_EVENT);

        //get shipment details
        Shipment shipment = getShipmentDetails(shipmentId);
        if (event.equalsIgnoreCase(ShipmentEvent.CREATE_EVENT.getEventName())) {
            //process shipment
            shipmentProcessor.processShipment(shipment, headers);
        } else if (event.equalsIgnoreCase(ShipmentEvent.CANCEL_REQUEST_EVENT.getEventName())) {
            //cancel shipment
            shipmentProcessor.cancelShipment(shipment, headers);
        } else {
            CvsLogger.error("Invalid event type received for shipment Id : " + shipmentId);
        }
    }

    // This method retrieves the shipment details using the provided shipment ID.
    private Shipment getShipmentDetails(String shipmentId) {
        return shipmentDetailsService.getShipmentDetails(shipmentId);
    }
}