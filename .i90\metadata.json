{"applicationId": "56b67b32-c43f-4208-b8c8-28ce4feb01a9", "applicationName": "omni-domain-oms-shipments-outbound-broker", "applicationDesc": "This is a broker which handles sending shipment updates to external systems.", "appFramework": "SPRINGBOOT", "appFeatures": ["WEB", "KAFKA_CONSUMER", "KAFKA_PRODUCER"], "authType": "NO_AUTH", "lineOfBusiness": "PHARMACY", "appName": "Omni Pharmacy", "clientName": "fullfillment_and_notifications", "minResponseTimeInMs": 100, "maxResponseTimeInMs": 300, "averageResponseTimeInMs": 200, "responseTimeThresholdInMs": 1500, "minTps": 50, "maxTps": 500, "avgTps": 250, "circuitBreakerErrorRate": 15, "memberEventInd": false, "primaryDataCenter": "GCP_EAST", "secondaryDataCenter": "GCP_WEST", "status": "PENDING", "active": true, "updatedBy": "<PERSON>", "updatedDate": "2025-05-20T17:11:39.813202524", "createdBy": "<PERSON>", "createdDate": "2025-05-20T17:10:39.117", "alerts": {"thresholdApiAlertsInd": true, "thresholdApiAlertsSeverity": "WARNING", "thresholdApiAlertsValue": 500, "availabilityApiAlertsInd": true, "availabilityApiAlertsSeverity": "WARNING", "availabilityApiAlertsValue": 98, "tpsApiAlertsInd": true, "tpsApiAlertsSeverity": "WARNING", "tpsApiAlertsValue": 1000, "channel": "slack", "groupName": "i90-OnboardingEligiblity", "owner": "app"}, "experimentalFlag": false}