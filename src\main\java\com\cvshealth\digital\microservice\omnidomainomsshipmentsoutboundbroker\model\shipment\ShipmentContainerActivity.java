package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.DatabaseProgId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentContainerActivity {

    private long shipmentContainerActivityId;
    private long shipmentContainerId;
    private String activityCode;
    private Instant activityTimestamp;
    private Instant creationTimestamp;
    private Instant lastModifiedTimestamp;
    private DatabaseProgId createProgId;
    private DatabaseProgId modifyProgId;


}
