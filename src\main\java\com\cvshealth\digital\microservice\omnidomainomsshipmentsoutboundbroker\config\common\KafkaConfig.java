package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.common;


import com.cvshealth.digital.framework.service.kafka.config.DigitalKafkaInitializer;
import com.cvshealth.digital.framework.service.kafka.utils.KafkaServiceUtils;


import com.cvshealth.digital.framework.service.kafka.producer.DigitalKafkaProducer;

import com.cvshealth.digital.framework.service.kafka.config.properties.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Kafka configuration.
 *
 * <AUTHOR> Shah
 */
@Slf4j
@Configuration
public class KafkaConfig implements ApplicationListener<ApplicationReadyEvent> {
    /**
     * Kafka service properties.
     *
     * @return KafkaServiceProperties
     */
    @Bean
    @ConfigurationProperties(prefix = "service.kafka")
    public KafkaServiceProperties kafkaServiceProperties() {
        return new KafkaServiceProperties();
    }


    /**
     * Digital Kafka Initializer.
     *
     * @param kafkaServiceProperties
     * @param kafkaServiceUtils
     * @return
     */
    @Bean
    public DigitalKafkaInitializer digitalKafkaInitializer(KafkaServiceProperties kafkaServiceProperties, KafkaServiceUtils kafkaServiceUtils) {
        return new DigitalKafkaInitializer(kafkaServiceProperties, kafkaServiceUtils);
    }


    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        // on application ready event

        DigitalKafkaInitializer initializer = event.getApplicationContext().getBean(DigitalKafkaInitializer.class);
        initializer.bootstrapConsumers(event.getApplicationContext());

    }


    /**
     * Default digital kafka producer.
     *
     * @param KafkaServiceProperties
     * @return DigitalKafkaProducer
     */
    @Bean
    public DigitalKafkaProducer<String, String> digitalKafkaProducer(KafkaServiceProperties kafkaServiceProperties) {
        return new DigitalKafkaProducer<>(kafkaServiceProperties.getProducers().get("default"));
    }

}
