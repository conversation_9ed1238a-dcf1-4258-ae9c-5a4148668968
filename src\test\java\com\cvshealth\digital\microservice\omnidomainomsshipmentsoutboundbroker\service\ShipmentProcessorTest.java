package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ShipmentProcessorTest {

    private TestShipmentProcessor shipmentProcessor;
    private Shipment mockShipment;
    private Map<String, String> headers;

    @BeforeEach
    void setUp() {
        shipmentProcessor = new TestShipmentProcessor();
        
        mockShipment = new Shipment();
        mockShipment.setShipmentId(12345L);
        mockShipment.setShipmentNo("SH-12345");
        mockShipment.setShipmentType("SPECIALTY");
        mockShipment.setOrderNo("ORD-12345");
        mockShipment.setShipNode("NODE-001");
        
        headers = new HashMap<>();
        headers.put("shipment-id", "12345");
        headers.put("event", "SHIPMENT_CREATE_EVENT");
        headers.put("lob", "specialty");
    }

    @Test
    void testProcessShipment_WithValidShipmentAndHeaders_ShouldCallImplementation() {
        // Act
        shipmentProcessor.processShipment(mockShipment, headers);

        // Assert
        assertTrue(shipmentProcessor.processShipmentCalled);
        assertEquals(mockShipment, shipmentProcessor.lastProcessedShipment);
        assertEquals(headers, shipmentProcessor.lastProcessedHeaders);
    }

    @Test
    void testCancelShipment_WithValidShipmentAndHeaders_ShouldCallImplementation() {
        // Act
        shipmentProcessor.cancelShipment(mockShipment, headers);

        // Assert
        assertTrue(shipmentProcessor.cancelShipmentCalled);
        assertEquals(mockShipment, shipmentProcessor.lastCancelledShipment);
        assertEquals(headers, shipmentProcessor.lastCancelledHeaders);
    }

    @Test
    void testProcessShipment_WithNullShipment_ShouldCallImplementation() {
        // Act
        shipmentProcessor.processShipment(null, headers);

        // Assert
        assertTrue(shipmentProcessor.processShipmentCalled);
        assertNull(shipmentProcessor.lastProcessedShipment);
        assertEquals(headers, shipmentProcessor.lastProcessedHeaders);
    }

    @Test
    void testProcessShipment_WithNullHeaders_ShouldCallImplementation() {
        // Act
        shipmentProcessor.processShipment(mockShipment, null);

        // Assert
        assertTrue(shipmentProcessor.processShipmentCalled);
        assertEquals(mockShipment, shipmentProcessor.lastProcessedShipment);
        assertNull(shipmentProcessor.lastProcessedHeaders);
    }

    @Test
    void testCancelShipment_WithNullShipment_ShouldCallImplementation() {
        // Act
        shipmentProcessor.cancelShipment(null, headers);

        // Assert
        assertTrue(shipmentProcessor.cancelShipmentCalled);
        assertNull(shipmentProcessor.lastCancelledShipment);
        assertEquals(headers, shipmentProcessor.lastCancelledHeaders);
    }

    @Test
    void testCancelShipment_WithNullHeaders_ShouldCallImplementation() {
        // Act
        shipmentProcessor.cancelShipment(mockShipment, null);

        // Assert
        assertTrue(shipmentProcessor.cancelShipmentCalled);
        assertEquals(mockShipment, shipmentProcessor.lastCancelledShipment);
        assertNull(shipmentProcessor.lastCancelledHeaders);
    }

    @Test
    void testProcessShipment_WithEmptyHeaders_ShouldCallImplementation() {
        // Arrange
        Map<String, String> emptyHeaders = new HashMap<>();

        // Act
        shipmentProcessor.processShipment(mockShipment, emptyHeaders);

        // Assert
        assertTrue(shipmentProcessor.processShipmentCalled);
        assertEquals(mockShipment, shipmentProcessor.lastProcessedShipment);
        assertEquals(emptyHeaders, shipmentProcessor.lastProcessedHeaders);
    }

    @Test
    void testCancelShipment_WithEmptyHeaders_ShouldCallImplementation() {
        // Arrange
        Map<String, String> emptyHeaders = new HashMap<>();

        // Act
        shipmentProcessor.cancelShipment(mockShipment, emptyHeaders);

        // Assert
        assertTrue(shipmentProcessor.cancelShipmentCalled);
        assertEquals(mockShipment, shipmentProcessor.lastCancelledShipment);
        assertEquals(emptyHeaders, shipmentProcessor.lastCancelledHeaders);
    }

    @Test
    void testMultipleProcessShipmentCalls_ShouldUpdateState() {
        // Arrange
        Shipment shipment1 = new Shipment();
        shipment1.setShipmentId(11111L);
        
        Shipment shipment2 = new Shipment();
        shipment2.setShipmentId(22222L);
        
        Map<String, String> headers1 = new HashMap<>();
        headers1.put("shipment-id", "11111");
        
        Map<String, String> headers2 = new HashMap<>();
        headers2.put("shipment-id", "22222");

        // Act
        shipmentProcessor.processShipment(shipment1, headers1);
        shipmentProcessor.processShipment(shipment2, headers2);

        // Assert
        assertTrue(shipmentProcessor.processShipmentCalled);
        assertEquals(shipment2, shipmentProcessor.lastProcessedShipment); // Should be the last one
        assertEquals(headers2, shipmentProcessor.lastProcessedHeaders); // Should be the last one
    }

    @Test
    void testMultipleCancelShipmentCalls_ShouldUpdateState() {
        // Arrange
        Shipment shipment1 = new Shipment();
        shipment1.setShipmentId(11111L);
        
        Shipment shipment2 = new Shipment();
        shipment2.setShipmentId(22222L);
        
        Map<String, String> headers1 = new HashMap<>();
        headers1.put("shipment-id", "11111");
        
        Map<String, String> headers2 = new HashMap<>();
        headers2.put("shipment-id", "22222");

        // Act
        shipmentProcessor.cancelShipment(shipment1, headers1);
        shipmentProcessor.cancelShipment(shipment2, headers2);

        // Assert
        assertTrue(shipmentProcessor.cancelShipmentCalled);
        assertEquals(shipment2, shipmentProcessor.lastCancelledShipment); // Should be the last one
        assertEquals(headers2, shipmentProcessor.lastCancelledHeaders); // Should be the last one
    }

    @Test
    void testMixedProcessAndCancelCalls_ShouldMaintainSeparateState() {
        // Arrange
        Shipment processShipment = new Shipment();
        processShipment.setShipmentId(11111L);
        
        Shipment cancelShipment = new Shipment();
        cancelShipment.setShipmentId(22222L);
        
        Map<String, String> processHeaders = new HashMap<>();
        processHeaders.put("event", "SHIPMENT_CREATE_EVENT");
        
        Map<String, String> cancelHeaders = new HashMap<>();
        cancelHeaders.put("event", "CANCEL_REQUEST_EVENT");

        // Act
        shipmentProcessor.processShipment(processShipment, processHeaders);
        shipmentProcessor.cancelShipment(cancelShipment, cancelHeaders);

        // Assert
        assertTrue(shipmentProcessor.processShipmentCalled);
        assertTrue(shipmentProcessor.cancelShipmentCalled);
        
        assertEquals(processShipment, shipmentProcessor.lastProcessedShipment);
        assertEquals(processHeaders, shipmentProcessor.lastProcessedHeaders);
        
        assertEquals(cancelShipment, shipmentProcessor.lastCancelledShipment);
        assertEquals(cancelHeaders, shipmentProcessor.lastCancelledHeaders);
    }

    @Test
    void testShipmentProcessorInterface_HasCorrectMethods() {
        // Assert that the interface has the expected methods
        assertTrue(shipmentProcessor instanceof ShipmentProcessor);
        
        // Verify that the methods exist and can be called
        assertDoesNotThrow(() -> {
            shipmentProcessor.processShipment(mockShipment, headers);
            shipmentProcessor.cancelShipment(mockShipment, headers);
        });
    }

    /**
     * Test implementation of ShipmentProcessor interface for testing purposes
     */
    private static class TestShipmentProcessor implements ShipmentProcessor {
        
        boolean processShipmentCalled = false;
        boolean cancelShipmentCalled = false;
        
        Shipment lastProcessedShipment;
        Map<String, String> lastProcessedHeaders;
        
        Shipment lastCancelledShipment;
        Map<String, String> lastCancelledHeaders;

        @Override
        public void processShipment(Shipment shipment, Map<String, String> headers) {
            processShipmentCalled = true;
            lastProcessedShipment = shipment;
            lastProcessedHeaders = headers;
        }

        @Override
        public void cancelShipment(Shipment shipment, Map<String, String> headers) {
            cancelShipmentCalled = true;
            lastCancelledShipment = shipment;
            lastCancelledHeaders = headers;
        }
    }
}
