package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentAddress {

    private String firstName;
    private String middleName;
    private String lastName;
    private String addressLine1;
    private String addressLine2;
    private String emailId;
    private String dayPhone;
    private String preferredContactPhone;
    private String city;
    private String state;
    private String zipCode;
    private String country;
    private String addressType;
}
