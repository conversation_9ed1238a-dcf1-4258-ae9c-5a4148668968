package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.producer;

import com.cvshealth.digital.framework.service.kafka.producer.DigitalKafkaProducer;
import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.framework.service.logging.service.LogServiceContext;
import com.cvshealth.digital.framework.starter.exception.ApiErrors;
import com.cvshealth.digital.framework.starter.exception.api.ApiRequiredFieldMissingException;
import com.cvshealth.digital.framework.starter.exception.model.ServiceStatusCodes;
import com.cvshealth.digital.framework.starter.model.api.ApiResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.common.ApplicationProperties;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.HeaderConstants;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.ShipmentUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.Map;
import static com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.CVSConstants.ERROR_SERVERITY_CRITICAL;

@Component
public class EventPublisherService {


    private final DigitalKafkaProducer<String, String> digitalKafkaProducer;
    private final ApplicationProperties applicationProperties;

    private final ShipmentUtil shipmentUtil;

    public EventPublisherService(DigitalKafkaProducer<String, String> digitalKafkaProducer, ApplicationProperties applicationProperties, ShipmentUtil shipmentUtil) {
        this.digitalKafkaProducer = digitalKafkaProducer;
        this.applicationProperties = applicationProperties;
        this.shipmentUtil = shipmentUtil;
    }

    public void publishShipmentEvent(Map<String, String> headers) {
        String eventName = shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_EVENT);
        try{
            publishEvent("shipmentInboundEvent","", headers);
            LogServiceContext.addTags(eventName, "Y");
        } catch (Exception e) {
            LogServiceContext.addTags(eventName, "N");
            LogServiceContext.addTags(eventName, ERROR_SERVERITY_CRITICAL);
            CvsLogger.error("Error publishing NACK shipment created event", e);
        }
    }

    private ApiResponse publishEvent(String eventType, String request, Map<String, String> headers) throws ApiRequiredFieldMissingException {
        ApplicationProperties.EventConfig eventConfig = applicationProperties.getEvents().get(eventType);
        if (null == eventConfig || StringUtils.isBlank(eventConfig.getTopic())) {
            throw new ApiRequiredFieldMissingException(ApiErrors.buildRequiredFieldError("topic"));
        }
        String key = null;
        digitalKafkaProducer.sendMessage(eventConfig.getTopic(), key, request, headers);
        return new ApiResponse(ServiceStatusCodes.STATUS_CODE_0000_SUCCESS, ServiceStatusCodes.SUCCESS);
    }

}
