package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.rxdelivery;

import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.framework.service.logging.service.LogServiceContext;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.exception.OMSShipmentBrokerOutboundServiceException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.dma.DMAResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.oms.DeliveryInputMessage;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.request.Root;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.response.Response;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery.PNPOrderSubmitFailedException;
import io.github.resilience4j.retry.annotation.Retry;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.springframework.http.MediaType.APPLICATION_JSON;

public class RxDeliveryRestApiClient {

    @Value("${shipment.get-by-id-url}")
    private String getShipmentApiUrl;

    private final RestClient restClient;

    private final RestClient restClientWithSsl;

    @Value("${pnp.url}")
    String pnpOrderSubmitApiUrl;

    @Value("${dma.url}")
    String dmaURL;

    public RxDeliveryRestApiClient(@Qualifier("restClient") RestClient restClient, @Qualifier("restClientWithSsl") RestClient restClientWithSsl) {
        this.restClient = restClient;
        this.restClientWithSsl = restClientWithSsl;
    }


    @Retry(name="fetch-shipment-details", fallbackMethod = "fetchShipmentDetailsFallback")
    public Shipment invokeShipmentDetails(String shipmentId) {

        String url = UriComponentsBuilder
                .fromHttpUrl(getShipmentApiUrl)
                .pathSegment(shipmentId)
                .build()
                .toUriString();

        return restClient.get()
                .uri(url)
                .retrieve()
                .onStatus(HttpStatusCode::isError, (request, response) -> {
                    throw new OMSShipmentBrokerOutboundServiceException(response.getStatusText(),response.getStatusCode(), shipmentId);
                })
                .body(Shipment.class);
    }

    public Shipment fetchShipmentDetailsFallback(String shipmentId, OMSShipmentBrokerOutboundServiceException ex) {
        CvsLogger.error(ex.getShipmentId() + "-" + ex.getMessage(), ex);
        throw ex;
    }

    @Retry(name="dma-service")
    public void invokeDMAApi(DeliveryInputMessage deliveryInputMessage) {

        LogServiceContext.addTags("dmaURL", dmaURL);

        restClient.post()
                .uri(dmaURL)
                .contentType(APPLICATION_JSON)
                .body(deliveryInputMessage)
                .retrieve()
                .onStatus(HttpStatusCode::isError, (request, response) -> {
                    throw new OMSShipmentBrokerOutboundServiceException(response.getStatusText(), response.getStatusCode().value());
                })
                .body(DMAResponse.class);
    }

    @Retry(name="pnp-service")
    public Response invokePnpSSL(String strStoreNum, Root root) {

        try {
            String pnpStoreBasedUrl = formPnpUrlBasedOnStoreNum(strStoreNum);
            LogServiceContext.addTags("pnpStoreBasedUrl", pnpStoreBasedUrl);
            return restClientWithSsl.post()
                    .uri(pnpStoreBasedUrl)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                    .body(root)
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        if(response.getStatusCode().equals(HttpStatus.SERVICE_UNAVAILABLE)) {
                            throw new PNPOrderSubmitFailedException(response.getStatusText(), String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()));
                        }
                        throw new PNPOrderSubmitFailedException(response.getStatusText(), String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()));
                    })
                    .body(Response.class);
        } catch (ResourceAccessException exception) {
            throw new PNPOrderSubmitFailedException(exception.getMessage(), String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()));
        } catch (PNPOrderSubmitFailedException e) {
            throw e;
        } catch (Exception e) {
            throw new PNPOrderSubmitFailedException(e.getMessage(), String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    private String formPnpUrlBasedOnStoreNum(String strStoreNum) {
        if (strStoreNum.length() < 5) {
            strStoreNum = "00000".concat(strStoreNum);
            strStoreNum = strStoreNum.substring(strStoreNum.length() - 5);
        }
        Pattern pattern = Pattern.compile("-storeNbr-");
        Matcher matcher = pattern.matcher(pnpOrderSubmitApiUrl);
        return matcher.replaceAll(strStoreNum);
    }
}