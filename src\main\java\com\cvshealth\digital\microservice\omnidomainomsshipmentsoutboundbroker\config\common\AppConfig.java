package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.common;

import com.cvshealth.digital.framework.starter.service.encrypt.EncryptionService;
import com.cvshealth.digital.framework.starter.service.encrypt.JasyptEncryptionService;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * application configuration.
 *
 * <AUTHOR> Shah
 */
@Configuration
public class AppConfig {

    @Bean
    @ConfigurationProperties(prefix = "app")
    public ApplicationProperties applicationProperties() {
        return new ApplicationProperties();
    }

    @Bean
    public EncryptionService encryptionService() {
        return new JasyptEncryptionService();
    }
}