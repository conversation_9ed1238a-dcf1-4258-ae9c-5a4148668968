<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    <!-- ####################################### -->
    <!-- Console appender -->
    <!-- ####################################### -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
					%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{trackingID}] %logger{36}:%line - %msg%n%rEx{full, org, sun, java.lang.reflect}
      			</Pattern>
        </encoder>
    </appender>
    
    <appender name="EVENTLOGGER" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
					%msg%n
      			</Pattern>
        </encoder>
    </appender>

    <!-- ####################################### -->
    <!-- Loggers  - Common for all env -->
    <!-- ####################################### -->
    
    <!-- NON-PROD -->
	<springProfile
		name="local,dev,dev1,dev2,dev3,qa,qa1,qa2,qa3,sit,sit1,sit2,sit3">
		
		<logger name="com.cvshealth" level="debug" additivity="false">
			<appender-ref ref="STDOUT" />
		</logger>
		
		<!-- REST low level request and response logs at trace level -->
		<!--
		<logger name="com.cvshealth.digital.framework.service.rest" level="trace" additivity="false">
			<appender-ref ref="STDOUT" />
		</logger>
		-->
				
		<!-- CVSEVENT logs at info level -->
		<logger name="CvsEventLogger" level="info" additivity="false">
			<appender-ref ref="EVENTLOGGER" />
		</logger>
	
		<root level="error">
			<appender-ref ref="STDOUT" />
		</root>
		
	</springProfile>
	
	<!-- PT  -->
	<springProfile
		name="pt, pt1, pt2, pt3, pt-dr, pteast, pt-east, east-pt, eastpt, ptwest, west-pt, westpt, pt-west, pt-east-east, pt-west-west, pt-west-east, pt-east-west">
		
		<logger name="com.cvshealth" level="error" additivity="false">
			<appender-ref ref="STDOUT" />
		</logger>
		
		<!-- CVSEVENT logs at info level -->
		<logger name="CvsEventLogger" level="info" additivity="false">
			<appender-ref ref="EVENTLOGGER" />
		</logger>
	
		<root level="error">
			<appender-ref ref="STDOUT" />
		</root>
		
	</springProfile>
	
	<!-- BETA  -->
	<springProfile
		name="beta, beta-east, beta-west">
		
		<logger name="com.cvshealth" level="error" additivity="false">
			<appender-ref ref="STDOUT" />
		</logger>
		
		<!-- CVSEVENT logs at info level -->
		<logger name="CvsEventLogger" level="info" additivity="false">
			<appender-ref ref="EVENTLOGGER" />
		</logger>
	
		<root level="error">
			<appender-ref ref="STDOUT" />
		</root>
		
	</springProfile>
	
	<!-- PROD -->
	<springProfile
		name="default, prod, prod1, prod2, prod-dr, prod-east, prod-west, prodeast, prodwest, prod-east-east,prod-west-east,prod-east-west,prod-west-west">
		
		<logger name="com.cvshealth" level="error" additivity="false">
			<appender-ref ref="STDOUT" />
		</logger>
		
		<!-- CVSEVENT logs at info level -->
		<logger name="CvsEventLogger" level="info" additivity="false">
			<appender-ref ref="EVENTLOGGER" />
		</logger>
	
		<root level="error">
			<appender-ref ref="STDOUT" />
		</root>
		
	</springProfile>
	
</configuration>