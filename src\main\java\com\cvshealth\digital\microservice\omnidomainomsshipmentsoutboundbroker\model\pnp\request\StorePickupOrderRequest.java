
package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StorePickupOrderRequest {

    private OrderInfo orderInfo;
    private ShipmentCarrierInfo shipmentCarrierInfo;
    private List<PaymentInfo> paymentInfo;
    private AdditionalOrderInfo additionalOrderInfo;
    private OrderPickUpInfo orderPickUpInfo;

}
