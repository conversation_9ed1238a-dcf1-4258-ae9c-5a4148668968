#namespace, app version and image details
appData:
  nameSpace: oms-qa
  dockerHub: cvsh.jfrog.io
  dockerImageGroupName: cvsdigital-docker/ghemu/cvs-health-source-code/omni-domain-oms-shipments-outbound-broker
  dockerImageName: omni-domain-oms-shipments-outbound-broker
  dockerImageTag: 1.0.28



#hpa settings - min and max replicas and minAvailable for PDB
hpaSettings:
  minAvailable: 1
  minReplicas: 1
  maxReplicas: 1
  averageUtilization: 80


# Pod resource requests and limits
resources:
  requests:
    cpu: "100m"
    memory: "768Mi"
  limits:
    cpu: "500m"
    memory: "1Gi"


# environment variables for app - secret values come from vault
# provide last property value in quotes (Else ArgoCD fails to deploy 11/03/2022)
envVars: |
  - name: ENV
    value: rxdelivery, qa

# env secrets to be referred as ENV variabes.
envSecrets: |
  - secretRef:
      name: oms-shipment-outbound-broker
      optional: false

#
#extraVolumeMounts: |
#  - name: my-common-certs
#    mountPath: /opt/digital/microservices/omni-domain-oms-shipment-service/certs
#    readOnly: true
#
#extraVolumes: |
#  - name: my-common-certs
#    secret:
#      secretName: my-common-certs
#      optional: false
