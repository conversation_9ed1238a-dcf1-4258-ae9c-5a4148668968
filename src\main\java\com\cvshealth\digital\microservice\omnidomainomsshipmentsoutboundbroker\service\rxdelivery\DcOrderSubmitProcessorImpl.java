package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.OMSFetchShipmentResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@RequiredArgsConstructor
@Slf4j
public class DcOrderSubmitProcessorImpl implements ShipmentOrderSubmitProcessor {

    private final ShipmentService shipmentService;

    @Override
    public void processShipmentOrderEvent(OMSFetchShipmentResponse omsShipment, Shipment shipment) {
        log.info("Publishing message to DC");
        shipmentService.publishMessageTODc(omsShipment);
    }

    @Override
    public boolean handlesShipmentType(String shipmentType) {
        // TODO check on OTCHS shipment type won't work in all cases. there are otchs shipmenttype orders which goes
        //      to PNP also. we need to have this check on the fulfillment location like PNP, DC etc.
        //      it would not have created problem now but once frontstore, otchs orders comes to this flow, this will break.
        return "OTCHS".equalsIgnoreCase(shipmentType);
    }
}
