package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.mapper.specialty;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.*;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request.ExternalEntityCreateRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request.Order;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request.Patient;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request.Rx;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class ExternalEntityMapper {

    /**
     * Map the shipment details to ExternalEntityCreateRequest
     * @param shipment
     * @return ExternalEntityCreateRequest
     */
    public ExternalEntityCreateRequest mapToExternalEntityRequest(Shipment shipment){
        ExternalEntityCreateRequest externalEntityRequest = new ExternalEntityCreateRequest();
        externalEntityRequest.setOrder(getOrder(shipment));
        externalEntityRequest.getOrder().setPatient(getPatient(shipment));
        return externalEntityRequest;
    }

    /**
     * Get the order details
     * @param shipment
     * @return Order
     */
    private Order getOrder(Shipment shipment) {
        Order order = new Order();
        order.setShipmentId(shipment.getShipmentId());
        order.setShipmentNo(shipment.getShipmentNo());
        ShipmentAddress shipToAddress = getAddressByType(shipment, "SHIP_TO");
        if(null != shipToAddress) {
            order.setShipToFirstname(shipToAddress.getFirstName());
            order.setShipToMiddleInitial(shipToAddress.getMiddleName());
            order.setShipToLastname(shipToAddress.getLastName());
            order.setShipToAddress1(shipToAddress.getAddressLine1());
            order.setShipToAddress2(shipToAddress.getAddressLine2());
            order.setShipToCity(shipToAddress.getCity());
            order.setShipToState(shipToAddress.getState());
            order.setShipToZip(shipToAddress.getZipCode());
        }
        order.setShipViaDesc(shipment.getCarrierServiceDescription());
        ShipmentAddress shipFromAddress = getAddressByType(shipment, "SHIP_FROM");
        if(null != shipFromAddress) {
           order.setCsAreaCode(shipFromAddress.getPreferredContactPhone().substring(0, 3));
           order.setCsPhone(shipFromAddress.getPreferredContactPhone().substring(4));
        }
        order.setShipPrefCarrier(shipment.getPreferredCarrier());
        order.setColdPackFlag(shipment.isColdpackIn());
        order.setDueDate(shipment.getShipmentItemList().stream().findFirst().get().getNeedsDt());
        order.setNeedsOnByIndicator(shipment.getShipmentItemList().stream().findFirst().get().getNeedsOnByCd());

        Attributes attributes = shipment.getAttributes();
        Clients clients = shipment.getClients();
        if(attributes != null){
            order.setCustLangCode(attributes.getPreferredLanguage());
            order.setPickListComments(attributes.getPickComments());
            order.setPackerInstructions(attributes.getPackerInstructions());
            order.setAdditionalRxLabel(attributes.isAdditionalRxLabel());
            order.setOrderDestCode(attributes.isSpecialtyAtRetailOrder());
            order.setPreferredLanguage(attributes.getPreferredLanguage());
            order.setBundleNote(attributes.getBundleInstructions());
        }
        if(clients != null){
            order.setClientCode(clients.getPayorId());
            order.setBrandCode(clients.getBrandCode());
        }
        order.setPatientCount(1);//TODO need to visit for white label orders sent to Drs
        order.setRxCount(shipment.getShipmentItemList().size());
        ShipmentAddress feAddress = getAddressByType(shipment, "FRONT_END_PHARMACY");
        if(feAddress != null){
            order.setFeSiteAddress(feAddress.getAddressLine1() + feAddress.getAddressLine2());
            order.setFeSiteCity(feAddress.getCity());
            order.setFeSiteState(feAddress.getState());
            order.setFeSiteZip(feAddress.getZipCode());
            order.setFeSitePhone(feAddress.getPreferredContactPhone());
        }
        order.setFeSiteDEA(shipment.getFillFromLocationDea());
        ShipmentPrints shipmentPrints = shipment.getShipmentPrints();
        if(shipmentPrints != null){
            order.setPrintPackPageCount(shipmentPrints.getPageCount());
            order.setPrintPackFileName(shipmentPrints.getName());
        }
        order.setShipViaMethod(shipment.getDeliveryCode());
        order.setDotFlag(shipment.isHazardous());
        order.setUspsProhibit(shipment.isUspsProhibitedIn());
        order.setSiteID(shipment.getShipNode());
        order.setShippingOptions(shipment.getShippingOptions());
        order.setCarrierShipCd(shipment.getCarrierShipCd());
        order.setCarrierServiceCd(shipment.getCarrierServiceCode());
        return order;
    }

    /**
     * Get the patient details
     * @param shipment
     * @return Patient
     */
    private Patient getPatient(Shipment shipment) {
        Patient patient = new Patient();
        patient.setPatientId(shipment.getCustomerProfileId());
        ShipmentAddress customerAddress = getAddressByType(shipment, "CUSTOMER_ADDRESS");
        if(customerAddress != null) {
            patient.setFirstName(customerAddress.getFirstName());
            patient.setMiddleInitial(customerAddress.getMiddleName());
            patient.setLastName(customerAddress.getLastName());
            patient.setAddressLine1(customerAddress.getAddressLine1());
            patient.setAddressLine2(customerAddress.getAddressLine2());
            patient.setCity(customerAddress.getCity());
            patient.setState(customerAddress.getState());
            String zipCode = customerAddress.getZipCode();
            if(zipCode != null && zipCode.length() == 9) {
                patient.setZip(zipCode.substring(0,5));
                patient.setZipSuffix(zipCode.substring(5));
            } else if(zipCode != null && zipCode.length() == 5) {
                patient.setZip(zipCode.substring(0,5));
            }
        }
        patient.setRx(getRxlist(shipment));
        patient.setRxCount(getRxlist(shipment).size());
        return patient;
    }

    /**
     * Get the list of Rx
     * @param shipment
     * @return List<Rx>
     */
    private List<Rx> getRxlist(Shipment shipment) {
        List<Rx> rxList = new ArrayList<>();
        shipment.getShipmentItemList().stream().forEach(
                shipmentItem -> {
                    Rx rx = new Rx();
                    rx.setPatientId(shipment.getCustomerProfileId());
                    rx.setRxNumber(shipmentItem.getRxNumber());
                    rx.setDrugProductId(shipmentItem.getItemId());
                    rx.setQuantityRequired(shipmentItem.getAllocatedQuantity());
                    rx.setRefillMsg(shipmentItem.getPrescriptionExtn().getRefillsLeft());
                    rx.setFillNumber(shipmentItem.getFillNumber());
                    rx.setNdcNum(shipmentItem.getNdcNumber());
                    rx.setCustDrugId(shipmentItem.getItemId());
                    rx.setSafetyCap(shipment.getAttributes().isSafetyCapRequired());
                    rx.setDrugDesc(shipmentItem.getItemDesc());
                    rx.setBrandName(shipmentItem.getPrescriptionExtn().getSubstitutedFor());
                    rx.setMfrCode(shipmentItem.getManufacturerName());
                    rx.setUnitOfMeasure(shipmentItem.getUnitOfMeasure());
                    rx.setDiscardDate(shipmentItem.getPrescriptionExtn().getDiscardDate());
                    rx.setBrandGenericText(shipmentItem.getPrescriptionExtn().getSubstitutedForVerbiage());
                    rx.setInstruction(shipmentItem.getPrescriptionExtn().getInstructions());
                    rx.setDrFirstname(shipmentItem.getPrescriptionExtn().getPrescriberFirstName());
                    rx.setDrMiddleName(shipmentItem.getPrescriptionExtn().getPrescriberMiddleInitials());
                    rx.setDrLastname(shipmentItem.getPrescriptionExtn().getPrescriberLastName());
                    rx.setDrType(shipmentItem.getPrescriptionExtn().getPrescriberType());
                    rx.setDrDeaNum(shipmentItem.getPrescriptionExtn().getPrescriberDEA());
                    rx.setCustLangCode(shipment.getAttributes().getPreferredLanguage());
                    rx.setLotNumber(shipmentItem.getLotNb());
                    rx.setPv1Id(shipmentItem.getPrescriptionExtn().getRphInitials());
                    rx.setDispensedQuantity(shipmentItem.getOrderedQuantity());
                    rx.setUom(shipmentItem.getItemForm());
                    rx.setDaysSupply(shipmentItem.getPrescriptionExtn().getDaysSupply());
                    rx.setLongSigFlag(shipmentItem.getPrescriptionExtn().isAdditionalInstructionsIncluded());
                    rx.setRxAuxMessage(shipmentItem.getPrescriptionExtn().getAuxMessage());
                    rx.setItemGenericName(shipmentItem.getGenericName());
                    rxList.add(rx);
                }
        );
        return rxList;
    }
    /**
     * Get the address by type
     * @param shipment
     * @param shipmentAddressType
     * @return ShipmentAddress
     */

    ShipmentAddress getAddressByType(Shipment shipment, String shipmentAddressType){
        ShipmentAddress address = shipment.getShipmentAddressList().stream().filter(
                shipmentAddress -> shipmentAddress.getAddressType().equalsIgnoreCase(shipmentAddressType)).findFirst().get();
        return address;
    }

}
