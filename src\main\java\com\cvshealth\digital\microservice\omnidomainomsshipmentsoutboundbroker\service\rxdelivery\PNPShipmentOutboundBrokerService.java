package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery;

import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.framework.service.logging.service.LogServiceContext;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.OMSFetchShipmentResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.rxdelivery.RxDeliveryRestApiClient;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.oms.DeliveryInputMessage;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.request.Root;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.response.Response;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.CVSCommonUtil;

import lombok.extern.slf4j.Slf4j;

import static com.cvs.oms.common.util.ResourceHelper.resolve;
import static com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.CVSConstants.*;


@Slf4j
public class PNPShipmentOutboundBrokerService {

    private final RxDeliveryRestApiClient restApiClient;
    private final PNPOrderMapper pnpOrderMapper;
    private final OMSMapper omsMapper;
    private final ErrorReprocessorService errorReprocessorService;

    public PNPShipmentOutboundBrokerService(RxDeliveryRestApiClient restApiClient, PNPOrderMapper pnpOrderMapper, OMSMapper omsMapper, ErrorReprocessorService errorReprocessorService) {
        this.restApiClient = restApiClient;
        this.pnpOrderMapper = pnpOrderMapper;
        this.omsMapper = omsMapper;
        this.errorReprocessorService = errorReprocessorService;
    }


    public Response submitOrderToPNP(Shipment shipmentIn, OMSFetchShipmentResponse omsFetchShipmentResponse, boolean retryFlow) {

        log.debug("Submitting order to PNP for shipment ID: {} and shipment number: {}", shipmentIn.getShipmentId(), omsFetchShipmentResponse.getShipmentDetails().getShipmentNo());

        addLoggerTags(shipmentIn,omsFetchShipmentResponse,retryFlow);

        try {
            Root root = pnpOrderMapper.mapShipment(omsFetchShipmentResponse);
            String strStoreNum = omsFetchShipmentResponse.getShipmentDetails().getShipNode();
            Response pnpResponse = restApiClient.invokePnpSSL(strStoreNum, root);

            log.debug("Received response from PNP for shipment ID: {} and shipment number: {}", shipmentIn.getShipmentId(), omsFetchShipmentResponse.getShipmentDetails().getShipmentNo());

            if (null != pnpResponse && null != pnpResponse.getStorePickupOrderResponse() && null != pnpResponse.getStorePickupOrderResponse().getStatus()) {

                log.debug("Received response with status: {} for shipment ID: {} and shipment number: {}", pnpResponse.getStorePickupOrderResponse().getStatus(), shipmentIn.getShipmentId(), omsFetchShipmentResponse.getShipmentDetails().getShipmentNo());

                String responseCode = resolve(() -> pnpResponse.getStorePickupOrderResponse().getStatus().getCode()).orElse("9999");
                String responseMsg = resolve(() -> pnpResponse.getStorePickupOrderResponse().getStatus().getMessage()).orElse("No response message");
                LogServiceContext.addTags(TAG_PNP_RESPONSE_CODE, responseCode);
                LogServiceContext.addTags(TAG_PNP_RESPONSE_MSG, responseMsg);

                if (responseCode.equalsIgnoreCase(PNP_SUCCESS_RESPONSE_CODE) || responseCode.equalsIgnoreCase(PNP_DUPLICATE_ORDER_RESPONSE_CODE)) {
                    log.debug("Successfully submitted order to PNP for shipment ID: {} and shipment number: {}", shipmentIn.getShipmentId(), omsFetchShipmentResponse.getShipmentDetails().getShipmentNo());
                    LogServiceContext.addTags(TAG_SUBMIT_TO_PNP_SUCCESS, VALUE_YES);
                    publishUpdateToOMS(root);
                } else {
                    log.warn("Failed to submit order to PNP for shipment ID: {} and shipment number: {}", shipmentIn.getShipmentId(), omsFetchShipmentResponse.getShipmentDetails().getShipmentNo());

                    LogServiceContext.addTags(TAG_SUBMIT_TO_PNP_SUCCESS, VALUE_NO);

                    if (retryFlow) {
                        throw new PNPOrderSubmitFailedException(responseMsg, responseCode, pnpResponse);
                    } else {
                        triggerErrorReprocessorFlow(shipmentIn, responseMsg, responseCode);
                    }
                }
            }
            return pnpResponse;
        } catch (PNPOrderSubmitFailedException e) {
            LogServiceContext.addTags(TAG_SUBMIT_TO_PNP_SUCCESS, VALUE_NO);
            LogServiceContext.addTags(TAG_ERROR_SERVERITY, ERROR_SERVERITY_LOW);
            LogServiceContext.addTags(TAG_PNP_RESPONSE_CODE, e.getErrorCode());
            LogServiceContext.addTags(TAG_PNP_RESPONSE_MSG, e.getMessage());
            CvsLogger.error("Error submitting shipment to PNP", e);
            if (!retryFlow) {
                triggerErrorReprocessorFlow(shipmentIn, e.getMessage(), e.getErrorCode());
                return new Response();
            } else {
                throw e;
            }
        }
    }

    private void publishUpdateToOMS(Root root) {
        try {
            DeliveryInputMessage deliveryInputMessage = omsMapper.mapDeliveryInputMessage(root.getStorePickupOrderRequest().getOrderInfo());
            restApiClient.invokeDMAApi(deliveryInputMessage);
            LogServiceContext.addTags(TAG_SUBMIT_TO_OMS_SUCCESS, VALUE_YES);
        } catch (Exception e) {
            LogServiceContext.addTags(TAG_ERROR_SERVERITY, ERROR_SERVERITY_CRITICAL);
            LogServiceContext.addTags(TAG_SUBMIT_TO_OMS_SUCCESS, VALUE_NO);
            CvsLogger.error("Error submitting order to OMS", e);
        }
    }

    private void triggerErrorReprocessorFlow(Shipment shipment, String errorMessage, String errorCode) {
        errorReprocessorService.reprocessFailedFlow(CVSCommonUtil.toJSONString(shipment), String.valueOf(shipment.getShipmentId()), errorMessage, errorCode, "pnpOrderSubmit");

    }

    private void addLoggerTags(Shipment shipmentIn, OMSFetchShipmentResponse omsFetchShipmentResponse, boolean retryFlow){
        LogServiceContext.addTags(TAG_RETRY_FLOW, retryFlow);
        LogServiceContext.addTags(TAG_SHIPMENT_ID, omsFetchShipmentResponse.getShipmentDetails().getShipmentId());
        LogServiceContext.addTags(TAG_FULFILLMENT_CHANNEL, shipmentIn.getFulfillmentChannel());
        LogServiceContext.addTags(TAG_SHIPMENT_NO, omsFetchShipmentResponse.getShipmentDetails().getShipmentNo());
        LogServiceContext.addTags(TAG_SHIPMENT_TYPE, omsFetchShipmentResponse.getShipmentDetails().getShipmentType());
        LogServiceContext.addTags(TAG_DELIVERY_CODE, omsFetchShipmentResponse.getShipmentDetails().getDeliveryCode());
        LogServiceContext.addTags(TAG_SHIP_NODE, omsFetchShipmentResponse.getShipmentDetails().getShipNode());
    }
}