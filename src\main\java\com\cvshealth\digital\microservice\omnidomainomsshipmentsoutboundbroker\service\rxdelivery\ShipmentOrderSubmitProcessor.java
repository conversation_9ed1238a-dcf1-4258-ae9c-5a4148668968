package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery;


import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.OMSFetchShipmentResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;

public interface ShipmentOrderSubmitProcessor {

    void processShipmentOrderEvent(OMSFetchShipmentResponse omsShipment, Shipment shipment);

    boolean handlesShipmentType(String shipmentType);
}
