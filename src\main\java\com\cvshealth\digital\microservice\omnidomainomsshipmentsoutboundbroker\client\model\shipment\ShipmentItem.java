package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentItem {

    private int shipmentItemId;
    private int shipmentId;
    private String shipToStoreNo;
    private int shipmentItemNo;
    private String orderLineNo;
    private String itemId;
    private String unitWeight;
    private double netWeight;
    private int quantity;
    private int orderedQuantity;
    private String itemDesc;
    private int shortedQty;
    private String lineCancellationReason;
    private String creationTimestamp;
    private String lastModifiedTimestamp;
    private double lineTotalWithoutTax;
    private double unitPrice;
    private double originalUnitPrice;
    private double lineTax;
    private String fillNumber;
    private String fillSeqNum;
    private String fillVerNum;
    private String rxNumber;
    private String memberId;
    private String rxStatus;
    private String itemType;
    private String upcNumber;
    private String[] alternateUpcList;
    private String substitutionItemList;
    private boolean fsaItem;
    private String rxNDC;
    private boolean pickable;
    private boolean hazmat;
    private boolean substitutionAllowed;
    private ShipmentItemExtn shipmentItemExtn;
}
