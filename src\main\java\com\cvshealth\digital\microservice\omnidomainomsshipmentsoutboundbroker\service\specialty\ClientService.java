package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.specialty;

import com.cvshealth.digital.framework.service.logging.utils.json.ZonedLocalTimeSerializer;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.common.StringTrimModule;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty.ClientConfig;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty.ClientProperties;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty.ExternalEntityRestClientCache;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.HeaderConstants;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.ShipmentEvent;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.ShipmentEventSource;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.exception.OMSShipmentBrokerOutboundServiceException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request.ExternalEntityCancelRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request.ExternalEntityCreateRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.response.ExternalEntityResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.producer.EventPublisherService;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.RestClient;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import static java.time.Instant.now;

@Slf4j
public class ClientService {

    private final ObjectMapper objectMapper;
    private final ClientProperties clientProperties;
    private final Map<String, CachedToken> tokenCache = new ConcurrentHashMap<>();
    private final EventPublisherService eventPublisherService;
    private final RestClient restClient;
    private final ExternalEntityRestClientCache externalEntityRestClientCache;


    public ClientService(ObjectMapper objectMapper, ClientProperties clientProperties, EventPublisherService eventPublisherService, RestClient restClient, ExternalEntityRestClientCache externalEntityRestClientCache) {
        this.objectMapper = objectMapper;
        this.clientProperties = clientProperties;
        this.eventPublisherService = eventPublisherService;
        this.restClient = restClient;
        this.externalEntityRestClientCache = externalEntityRestClientCache;
    }

    public ClientConfig getClientConfig(String name){
        return clientProperties.getConfigs().get(name);
    }

    /*
     * This method retrieves the access token for the given client configuration.
     * It checks if a valid token is already cached; if not, it fetches a new one.
     */
    public String getAccessToken(ClientConfig clientConfig) {
        String clientName = clientConfig.getClientName();
        CachedToken cachedToken = tokenCache.get(clientName);
        if(cachedToken != null && now().isBefore(cachedToken.expiryTime)) {
            log.debug("Using cached token for client: " + clientName);
            return cachedToken.accessToken;
        }
        log.debug("Fetching new access token for client: " + clientName);
        String newAccessToken = getNewAccessToken(clientConfig);
        tokenCache.put(clientName, new CachedToken(newAccessToken, extractTokenExpiry(newAccessToken)));
        return newAccessToken;
    }

    /*
     * This method submits an order to the external entity using the provided shipment ID, client configuration, and request body.
     * It retrieves the access token internally and handles retries and circuit breaking.
     * If access token retrieval or order submission fails, resilience4j will retry and eventually call the fallback method.
     */
    @Retry(name="submit-order-external-service-retry", fallbackMethod="submitOrderToExternalEntityFallback")
    @CircuitBreaker(name="submit-order-external-service-circuit-breaker")
    public void submitOrderToExternalEntity(long shipmentId, Map<String, String> msgHeaders, ClientConfig config, ExternalEntityCreateRequest reqBody) {
        ExternalEntityResponse response = null;
        String accessToken = null;

        if(!config.isTestFlag()) {
            accessToken = getAccessToken(config);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            headers.setContentType(MediaType.APPLICATION_JSON);
            try {
                log.debug("Order submit attempt to external entity for shipment ID: " + shipmentId + " and shipment number: " + reqBody.getOrder().getShipmentNo() + " client: " + config.getClientName() + " at " + now());
                RestClient cachedRestClient = externalEntityRestClientCache.getExternalEntityCachedRestClient(config);
                response = cachedRestClient.post()
                        .uri(config.getCreateShipmentUrl())
                        .headers(httpHeaders -> httpHeaders.addAll(headers))
                        .body(reqBody)
                        .retrieve()
                        .onStatus(HttpStatusCode::isError, (req, resp) -> {
                            throw new OMSShipmentBrokerOutboundServiceException(resp.getStatusText(), resp.getStatusCode(), reqBody.getOrder().getShipmentNo());
                        })
                        .body(ExternalEntityResponse.class);

            } catch (Exception ex) {
                throw new RuntimeException("Order Submit failed for shipment Id : " + shipmentId);
            }
        }
        //submit ACK event
        eventPublisherService.publishShipmentEvent( replaceHeaderEvent(ShipmentEvent.SHIPMENT_ACK_EVENT, msgHeaders));
    }

    /*
     * Fallback method for submitOrderToExternalEntity.
     * It publishes a NACK event and returns a failure response.
     */
    public void submitOrderToExternalEntityFallback(long shipmentId, Map<String, String> msgHeaders, ClientConfig config, ExternalEntityCreateRequest reqBody, RuntimeException ex) {
        //submit NACK event
        eventPublisherService.publishShipmentEvent(replaceHeaderEvent(ShipmentEvent.SHIPMENT_NACK_EVENT, msgHeaders));
        ExternalEntityResponse response = new ExternalEntityResponse();
        response.setStatus("Failure");
        log.error("Not able to submit order to external entity for shipment ID: " + shipmentId, ex);
    }

    @Retry(name="cancel-order-external-service-retry", fallbackMethod="cancelOrderToExternalEntityFallback")
    @CircuitBreaker(name="cancel-order-external-service-circuit-breaker")
    public void cancelOrderToExternalEntity(long shipmentId, Map<String, String> msgHeaders, ClientConfig config, ExternalEntityCancelRequest reqBody) {
        ExternalEntityResponse response = null;
        String accessToken = null;

        if(!config.isTestFlag()) {
            accessToken = getAccessToken(config);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            headers.setContentType(MediaType.APPLICATION_JSON);
            try {
                log.debug("Order cancel attempt to external entity for shipment ID: " + shipmentId + " and shipment number: " + reqBody.getShipmentNo() + " client: " + config.getClientName() + " at " + now());
                RestClient cachedRestClient = externalEntityRestClientCache.getExternalEntityCachedRestClient(config);
                response = cachedRestClient.post()
                        .uri(config.getCancelShipmentUrl())
                        .headers(httpHeaders -> httpHeaders.addAll(headers))
                        .body(reqBody)
                        .retrieve()
                        .onStatus(HttpStatusCode::isError, (req, resp) -> {
                            throw new OMSShipmentBrokerOutboundServiceException(resp.getStatusText(), resp.getStatusCode(), reqBody.getShipmentNo());
                        })
                        .body(ExternalEntityResponse.class);
            } catch (Exception ex) {
                throw new RuntimeException("Order cancel request failed for shipment Id : " + shipmentId);
            }
        }
        //cancel ACK event
        eventPublisherService.publishShipmentEvent(replaceHeaderEvent(ShipmentEvent.CANCEL_ACK_EVENT, msgHeaders));
    }

    public void cancelOrderToExternalEntityFallback(long shipmentId, Map<String, String> msgHeaders, ClientConfig config, ExternalEntityCancelRequest reqBody, RuntimeException ex) {
        //cancel NACK event
        eventPublisherService.publishShipmentEvent(replaceHeaderEvent(ShipmentEvent.CANCEL_NACK_EVENT, msgHeaders));
        ExternalEntityResponse response = new ExternalEntityResponse();
        response.setStatus("Failure");
        log.error("Not able to cancel order to external entity for shipment ID: " + shipmentId, ex);
    }

    /*
     * This method extracts the token expiry time from the JWT access token.
     * It decodes the JWT and retrieves the "exp" claim.
     */
    Instant extractTokenExpiry(String accessToken) {
        try{
            String[] chunks = accessToken.split("\\.");
            if(chunks.length < 2){
                throw new IllegalArgumentException("Invalid JWT format.");
            }
            String json = new String(Base64.getUrlDecoder().decode(chunks[1]));
            Map<String, Object> payload = objectMapper.readValue(json, Map.class);
            Integer expTimestamp = (Integer) payload.get("exp");
            return Instant.ofEpochSecond(expTimestamp);

        } catch(Exception e) {
            throw new RuntimeException("Failed to decode JWT token.");
        }
    }

    /*
     * This method retrieves a new access token from the external service.
     * It constructs the request body and makes a POST request to the token URL.
     */
    String getNewAccessToken(ClientConfig clientConfig) {
        String scopes = String.join(" ", clientConfig.getScopes());
        Map<String, String> reqBody = Map.of(
                "grant_type", clientConfig.getGrantType(),
                "client_id", clientConfig.getClientId(),
                "client_secret", clientConfig.getClientSecret(),
                "audience", clientConfig.getAudience(),
                "scope", scopes
        );

        String accessToken = null;
        Map<String, Object> response = null;

        try {
            response = restClient.post()
                    .uri(clientConfig.getTokenUrl())
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(reqBody)
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, (req, resp) -> {
                        throw new OMSShipmentBrokerOutboundServiceException(resp.getStatusText(), resp.getStatusCode(), clientConfig.getClientName());
                    })
                    .body(Map.class);

            if(response != null) {
                accessToken = (String) response.get("access_token");
            }
        } catch (Exception e) {
            throw new RuntimeException("Could not retrieve access token for client -> " + clientConfig.getClientName(), e);
        }
        return accessToken;
    }

    /*
     * This record stores the access token and its expiry time.
     */
    record CachedToken(String accessToken, Instant expiryTime) {
    }

    private Map<String, String> replaceHeaderEvent(ShipmentEvent event, Map<String, String> msgHeaders) {
        if (msgHeaders.containsKey(HeaderConstants.HEADER_EVENT)) {
            msgHeaders.put(HeaderConstants.HEADER_EVENT, event.getEventName());
        }
        if (msgHeaders.containsKey(HeaderConstants.HEADER_EVENT_SOURCE)) {
            msgHeaders.put(HeaderConstants.HEADER_EVENT_SOURCE, ShipmentEventSource.INTERNAL.getEventName());
        }
        if (msgHeaders.containsKey(HeaderConstants.HEADER_EVENT_TS)) {
            msgHeaders.put(HeaderConstants.HEADER_EVENT_TS, String.valueOf(Instant.now().getEpochSecond()));
        }
        return msgHeaders;
    }

}
