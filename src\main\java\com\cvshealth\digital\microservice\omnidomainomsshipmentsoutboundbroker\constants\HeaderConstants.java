package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants;

public class HeaderConstants {
    public static final String HEADER_LOB = "lob";
    public static final String HEADER_EVENT = "event";
    public static final String HEADER_SHIPMENT_ID = "shipment-id";
    public static final String HEADER_SHIPMENT_NUMBER = "shipment-no";
    public static final String HEADER_EVENT_SOURCE = "event-src";
    public static final String HEADER_EVENT_TS = "event-ts";

    // these headers are used for RxDelivery
    public static final String HEADER_SOURCE = "source";
    public static final String HEADER_ACTION = "action";
    public static final String HEADER_LINE_OF_BUSINESS = "lineOfBusiness";
    public static final String HEADER_ORDER_TYPE = "orderType";
    public static final String HEADER_ORDER_KEY = "orderKey";
    public static final String HEADER_TIMESTAMP = "timestamp";
    public static final String HEADER_CONVERSATION_ID = "conversationId";
 }
