
package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentInfo {

    private String shipMethod;
    private double shipCharge;
    private double shipTax;
    private List<ShipAddress> shipAddress;

}
