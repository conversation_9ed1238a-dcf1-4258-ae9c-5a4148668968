name: CI Main

on:
  push:
    branches:
      - main
    paths:
      - '**/src/**'
      - 'build.gradle'
      - 'gradle.properties'

jobs:
  build:
    uses: cvs-health-source-code/gha_workflow_actions/.github/workflows/gradle_docker.yaml@latest
    secrets: inherit
    with:
      JAVA_VERSION: "21.0.3"
      HARNESS_DEPLOY_FILE: "NA"
      SKIP_SEMANTIC: false
      JAVA_LINT_COMMAND: "NA"
      LIBRARY_PUBLISH: false
      SECURITY_GATE_TEAM_NAME: oms-unification
      JAVA_TEST_COMMAND: 'gradle clean build'
      SAST_TOOL: snyk
      SCA_TOOL: snyk
      CONTAINER_SCAN_TOOL: snyk
      SNYK_ORG: "************************************"
