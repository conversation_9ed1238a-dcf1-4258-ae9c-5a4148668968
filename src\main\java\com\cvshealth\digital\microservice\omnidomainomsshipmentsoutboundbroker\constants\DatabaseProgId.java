package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum DatabaseProgId {

    LEGACY_OMS("legacy_oms", "legacy_oms", "IBM Sterling OMS"),
    SPECIALTY_340B("specialty_340B", "specialty_340B", "340B CE external");

    private final String applicationName;
    private final String progId;
    private final String description;

    private static final Map<String, DatabaseProgId> progIdMap = new HashMap<>();

    static {
        for (DatabaseProgId e : DatabaseProgId.values()) {
            if (progIdMap.put(e.getProgId(), e) != null) {
                throw new IllegalArgumentException("invalid Prog Id: " + e.getProgId());
            }
        }
    }

    public static DatabaseProgId getProgId(String progId) {
        return progIdMap.get(progId);
    }

}
