package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.specialty;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty.ClientConfig;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty.ClientProperties;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty.ExternalEntityRestClientCache;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request.ExternalEntityCancelRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request.ExternalEntityCreateRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.producer.EventPublisherService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClient;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ClientServiceTest {

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private ClientProperties clientProperties;

    @Mock
    private EventPublisherService eventPublisherService;

    @Mock
    private RestClient restClient;

    @Mock
    private ExternalEntityRestClientCache externalEntityRestClientCache;

    @Mock
    private RestClient.RequestBodyUriSpec requestBodyUriSpec;

    @Mock
    private RestClient.RequestBodySpec requestBodySpec;

    @Mock
    private RestClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private RestClient.ResponseSpec responseSpec;

    private ClientService clientService;

    @BeforeEach
    void setUp() {
        clientService = new ClientService(objectMapper, clientProperties, eventPublisherService, restClient, externalEntityRestClientCache);
    }

    @Test
    void testCancelOrderToExternalEntity_Success_ShouldSendCancelAckEvent() {
        // Arrange
        long shipmentId = 12345L;
        Map<String, String> headers = new HashMap<>();
        headers.put("event", "CANCEL_REQUEST_EVENT");
        
        ClientConfig config = createTestClientConfig();
        config.setTestFlag(true); // Set test flag to bypass external call
        
        ExternalEntityCancelRequest request = new ExternalEntityCancelRequest();
        request.setShipmentId(shipmentId);
        request.setShipmentNo("SH-12345");

        // Act
        clientService.cancelOrderToExternalEntity(shipmentId, headers, config, request);

        // Assert
        verify(eventPublisherService).publishShipmentEvent(any(Map.class));
    }

    @Test
    void testCancelOrderToExternalEntityFallback_ShouldSendCancelNackEvent() {
        // Arrange
        long shipmentId = 12345L;
        Map<String, String> headers = new HashMap<>();
        headers.put("event", "CANCEL_REQUEST_EVENT");
        
        ClientConfig config = createTestClientConfig();
        ExternalEntityCancelRequest request = new ExternalEntityCancelRequest();
        RuntimeException exception = new RuntimeException("Test exception");

        // Act
        clientService.cancelOrderToExternalEntityFallback(shipmentId, headers, config, request, exception);

        // Assert
        verify(eventPublisherService).publishShipmentEvent(any(Map.class));
    }

    @Test
    void testSubmitOrderToExternalEntity_Success_ShouldSendAckEvent() {
        // Arrange
        long shipmentId = 12345L;
        Map<String, String> headers = new HashMap<>();
        headers.put("event", "SHIPMENT_CREATE_EVENT");
        
        ClientConfig config = createTestClientConfig();
        config.setTestFlag(true); // Set test flag to bypass external call
        
        ExternalEntityCreateRequest request = new ExternalEntityCreateRequest();

        // Act
        clientService.submitOrderToExternalEntity(shipmentId, headers, config, request);

        // Assert
        verify(eventPublisherService).publishShipmentEvent(any(Map.class));
    }

    @Test
    void testSubmitOrderToExternalEntityFallback_ShouldSendNackEvent() {
        // Arrange
        long shipmentId = 12345L;
        Map<String, String> headers = new HashMap<>();
        headers.put("event", "SHIPMENT_CREATE_EVENT");
        
        ClientConfig config = createTestClientConfig();
        ExternalEntityCreateRequest request = new ExternalEntityCreateRequest();
        RuntimeException exception = new RuntimeException("Test exception");

        // Act
        clientService.submitOrderToExternalEntityFallback(shipmentId, headers, config, request, exception);

        // Assert
        verify(eventPublisherService).publishShipmentEvent(any(Map.class));
    }

    @Test
    void testSubmitOrderToExternalEntity_WithTestFlag_ShouldNotCallExternalService() {
        // Arrange
        long shipmentId = 12345L;
        Map<String, String> headers = new HashMap<>();
        headers.put("event", "SHIPMENT_CREATE_EVENT");

        ClientConfig config = createTestClientConfig();
        config.setTestFlag(true); // Enable test flag to bypass external call

        ExternalEntityCreateRequest request = new ExternalEntityCreateRequest();

        // Act
        assertDoesNotThrow(() -> {
            clientService.submitOrderToExternalEntity(shipmentId, headers, config, request);
        });

        // Assert
        verify(eventPublisherService).publishShipmentEvent(any(Map.class));
        // Verify that no external calls were made
        verifyNoInteractions(restClient);
    }

    @Test
    void testCancelOrderToExternalEntity_WithTestFlag_ShouldNotCallExternalService() {
        // Arrange
        long shipmentId = 12345L;
        Map<String, String> headers = new HashMap<>();
        headers.put("event", "CANCEL_REQUEST_EVENT");

        ClientConfig config = createTestClientConfig();
        config.setTestFlag(true); // Enable test flag to bypass external call

        ExternalEntityCancelRequest request = new ExternalEntityCancelRequest();
        request.setShipmentId(shipmentId);
        request.setShipmentNo("SH-12345");

        // Act
        assertDoesNotThrow(() -> {
            clientService.cancelOrderToExternalEntity(shipmentId, headers, config, request);
        });

        // Assert
        verify(eventPublisherService).publishShipmentEvent(any(Map.class));
        // Verify that no external calls were made
        verifyNoInteractions(restClient);
    }

    @Test
    void testGetClientConfig_WithValidName_ShouldReturnConfig() {
        // Arrange
        String clientName = "BOB";
        ClientConfig expectedConfig = new ClientConfig();
        expectedConfig.setClientName(clientName);
        
        Map<String, ClientConfig> configs = new HashMap<>();
        configs.put(clientName, expectedConfig);
        
        when(clientProperties.getConfigs()).thenReturn(configs);
        
        // Act
        ClientConfig result = clientService.getClientConfig(clientName);
        
        // Assert
        assertNotNull(result);
        assertEquals(clientName, result.getClientName());
        assertSame(expectedConfig, result);
    }

    @Test
    void testGetClientConfig_WithInvalidName_ShouldReturnNull() {
        // Arrange
        String clientName = "INVALID_CLIENT";
        Map<String, ClientConfig> configs = new HashMap<>();
        
        when(clientProperties.getConfigs()).thenReturn(configs);
        
        // Act
        ClientConfig result = clientService.getClientConfig(clientName);
        
        // Assert
        assertNull(result);
    }

    @Test
    void testGetClientConfig_WithNullName_ShouldReturnNull() {
        // Arrange
        Map<String, ClientConfig> configs = new HashMap<>();
        when(clientProperties.getConfigs()).thenReturn(configs);
        
        // Act
        ClientConfig result = clientService.getClientConfig(null);
        
        // Assert
        assertNull(result);
    }

    @Test
    void testGetAccessToken_WithNullConfig_ShouldHandleGracefully() {
        // Act & Assert
        assertThrows(Exception.class, () -> {
            clientService.getAccessToken(null);
        });
    }

    /**
     * Helper method to create a test client configuration
     */
    private ClientConfig createTestClientConfig() {
        ClientConfig config = new ClientConfig();
        config.setClientName("TEST_CLIENT");
        config.setTokenUrl("https://auth.test.com/oauth/token");
        config.setGrantType("client_credentials");
        config.setAudience("https://api.test.com");
        config.setScopes(Arrays.asList("read", "write"));
        config.setClientId("test-client-id");
        config.setClientSecret("test-client-secret");
        config.setCreateShipmentUrl("https://api.test.com/v1/create");
        config.setCancelShipmentUrl("https://api.test.com/v1/cancel");
        config.setTestFlag(false);
        return config;
    }
}
