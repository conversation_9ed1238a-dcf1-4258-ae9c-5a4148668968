package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ShipmentStatus {

    SHIPMENT_CREATED("SHIPMENT_CREATED", "100", "Shipment Created"),
    SHIPMENT_ACKNOWLEDGED("SHIPMENT_ACKNOWLEDGED", "100.100", "Shipment Acknowledged"),
    SHIPMENT_WAVED("SHIPMENT_WAVED", "100.200", "Shipment Waved"),
    SHIPMENT_PACKED("SHIPMENT_PACKED", "100.300", "Shipment Packed"),
    SHIPMENT_LABELED("SHIPMENT_LABELED", "100.400", "Shipment Labeled"),
    SHIPMENT_PENDING("SHIPMENT_PENDING", "100.500", "Shipment Pending"),
    SHIPMENT_SHIPPED("SHIPMENT_SHIPPED", "200", "Shipment Shipped"),
    SHIPMENT_PACKED_BY_CARRIER("SHIPMENT_PACKED_BY_CARRIER", "200.100", "Shipment Picked By Carrier"),
    SHIPMENT_DELIVERED("SHIPMENT_DELIVERED", "200.200", "Shipment Delivered"),
    SHIPMENT_DELAYED("SHIPMENT_DELAYED", "200.300", "Shipment Delayed"),
    SHIPMENT_RETURNED_BY_CARRIER("SHIPMENT_RETURNED_BY_CARRIER", "200.400", "Shipment Returned By Carrier"),
    SHIPMENT_CANCELLED("SHIPMENT_CANCELLED", "900", "Shipment Cancelled");

    private final String statusName;
    private final String statusCode;
    private final String statusDescription;

    private static final Map<String, ShipmentStatus> shipmentStatusMap = new HashMap<>();

    static {
        for (ShipmentStatus e : ShipmentStatus.values()) {
            if (shipmentStatusMap.put(e.getStatusCode(), e) != null) {
                throw new IllegalArgumentException("invalid status code: " + e.getStatusCode());
            }
        }
    }

    public static ShipmentStatus getStatusCode(String code) {
        return shipmentStatusMap.get(code);
    }

}
