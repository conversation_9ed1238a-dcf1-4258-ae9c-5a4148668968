# Start with a base image containing Java runtime
FROM cvsh.jfrog.io/cvsdigital-docker-local/devsecops/distroless/java-21-distroless:latest

# Add a volume pointing to /tmp
VOLUME /tmp

# CD app directory
ENV SERVICE_PATH=/opt/digital/microservices/omni-domain-oms-shipments-outbound-broker
WORKDIR	$SERVICE_PATH

# Add the application's jar to the container
COPY build/libs/omni-domain-oms-shipments-outbound-broker-exec.jar $SERVICE_PATH

# Make port available to the world outside this container
EXPOSE 21000

# To avoid running as root
USER 9000:9000

# Run the jar file
ENTRYPOINT ["java", \
"-Xms128M", \
"-Djava.security.egd=file:/dev/./urandom ", \
"-Dspring.config.additional-location=/additional-config/application.yaml", \
"-Dspring.config.location=/additional-config/application.yaml", \
"-Dlogging.config=/additional-config/logback-spring.xml",\
"-Dspring.profiles.active=${ENV}", \
"-jar", \
"omni-domain-oms-shipments-outbound-broker-exec.jar"]