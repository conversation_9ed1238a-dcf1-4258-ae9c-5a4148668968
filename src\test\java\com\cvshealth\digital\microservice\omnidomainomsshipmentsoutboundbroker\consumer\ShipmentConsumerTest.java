package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer;

import com.cvshealth.digital.framework.service.kafka.exception.KafkaConsumerException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.HeaderConstants;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.ShipmentEvent;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentDetailsService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentProcessor;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.ShipmentUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ShipmentConsumerTest {

    @Mock
    private ShipmentDetailsService shipmentDetailsService;

    @Mock
    private ShipmentProcessor shipmentProcessor;

    @Mock
    private ShipmentUtil shipmentUtil;

    @Mock
    private HeaderValidator headerValidator;

    @Mock
    private ConsumerRecord<String, byte[]> consumerRecord;

    private ShipmentConsumer shipmentConsumer;
    private Map<String, String> headers;
    private Shipment mockShipment;

    @BeforeEach
    void setUp() {
        shipmentConsumer = new ShipmentConsumer(shipmentDetailsService, shipmentProcessor, shipmentUtil, headerValidator);
        
        headers = new HashMap<>();
        headers.put(HeaderConstants.HEADER_SHIPMENT_ID, "12345");
        headers.put(HeaderConstants.HEADER_EVENT, ShipmentEvent.CREATE_EVENT.getEventName());
        
        mockShipment = new Shipment();
        mockShipment.setShipmentId(12345L);
        mockShipment.setShipmentNo("SH-12345");
        mockShipment.setShipmentType("SPECIALTY");
        mockShipment.setOrderNo("ORD-12345");
        mockShipment.setShipNode("NODE-001");
    }

    @Test
    void testOnMessage_WithCreateEvent_ShouldProcessShipment() throws KafkaConsumerException {
        // Arrange
        when(headerValidator.validateAndGetHeaderMap(consumerRecord)).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID)).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_EVENT)).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(mockShipment);

        // Act
        Integer result = shipmentConsumer.onMessage("outbound-event", consumerRecord);

        // Assert
        assertEquals(0, result);
        verify(headerValidator).validateAndGetHeaderMap(consumerRecord);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_EVENT);
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verify(shipmentProcessor).processShipment(mockShipment, headers);
        verify(shipmentProcessor, never()).cancelShipment(any(), any());
    }

    @Test
    void testOnMessage_WithCancelEvent_ShouldCancelShipment() throws KafkaConsumerException {
        // Arrange
        headers.put(HeaderConstants.HEADER_EVENT, ShipmentEvent.CANCEL_REQUEST_EVENT.getEventName());
        when(headerValidator.validateAndGetHeaderMap(consumerRecord)).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID)).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_EVENT)).thenReturn(ShipmentEvent.CANCEL_REQUEST_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(mockShipment);

        // Act
        Integer result = shipmentConsumer.onMessage("outbound-event", consumerRecord);

        // Assert
        assertEquals(0, result);
        verify(headerValidator).validateAndGetHeaderMap(consumerRecord);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_EVENT);
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verify(shipmentProcessor).cancelShipment(mockShipment, headers);
        verify(shipmentProcessor, never()).processShipment(any(), any());
    }

    @Test
    void testOnMessage_WithInvalidEvent_ShouldLogError() throws KafkaConsumerException {
        // Arrange
        headers.put(HeaderConstants.HEADER_EVENT, "INVALID_EVENT");
        when(headerValidator.validateAndGetHeaderMap(consumerRecord)).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID)).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_EVENT)).thenReturn("INVALID_EVENT");
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(mockShipment);

        // Act
        Integer result = shipmentConsumer.onMessage("outbound-event", consumerRecord);

        // Assert
        assertEquals(0, result);
        verify(headerValidator).validateAndGetHeaderMap(consumerRecord);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_EVENT);
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verify(shipmentProcessor, never()).processShipment(any(), any());
        verify(shipmentProcessor, never()).cancelShipment(any(), any());
    }

    @Test
    void testOnMessage_WithHeaderValidationException_ShouldThrowKafkaConsumerException() {
        // Arrange
        when(headerValidator.validateAndGetHeaderMap(consumerRecord)).thenThrow(new RuntimeException("Header validation failed"));

        // Act & Assert
        KafkaConsumerException exception = assertThrows(KafkaConsumerException.class, () -> {
            shipmentConsumer.onMessage("outbound-event", consumerRecord);
        });

        assertEquals("Header validation failed", exception.getMessage());
        verify(headerValidator).validateAndGetHeaderMap(consumerRecord);
        verifyNoInteractions(shipmentUtil);
        verifyNoInteractions(shipmentDetailsService);
        verifyNoInteractions(shipmentProcessor);
    }

    @Test
    void testOnMessage_WithShipmentUtilException_ShouldThrowKafkaConsumerException() {
        // Arrange
        when(headerValidator.validateAndGetHeaderMap(consumerRecord)).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID)).thenThrow(new RuntimeException("Shipment ID not found"));

        // Act & Assert
        KafkaConsumerException exception = assertThrows(KafkaConsumerException.class, () -> {
            shipmentConsumer.onMessage("outbound-event", consumerRecord);
        });

        assertEquals("Shipment ID not found", exception.getMessage());
        verify(headerValidator).validateAndGetHeaderMap(consumerRecord);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID);
        verifyNoInteractions(shipmentDetailsService);
        verifyNoInteractions(shipmentProcessor);
    }

    @Test
    void testOnMessage_WithShipmentDetailsServiceException_ShouldThrowKafkaConsumerException() {
        // Arrange
        when(headerValidator.validateAndGetHeaderMap(consumerRecord)).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID)).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_EVENT)).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenThrow(new RuntimeException("Shipment not found"));

        // Act & Assert
        KafkaConsumerException exception = assertThrows(KafkaConsumerException.class, () -> {
            shipmentConsumer.onMessage("outbound-event", consumerRecord);
        });

        assertEquals("Shipment not found", exception.getMessage());
        verify(headerValidator).validateAndGetHeaderMap(consumerRecord);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_EVENT);
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verifyNoInteractions(shipmentProcessor);
    }

    @Test
    void testOnMessage_WithShipmentProcessorException_ShouldThrowKafkaConsumerException() {
        // Arrange
        when(headerValidator.validateAndGetHeaderMap(consumerRecord)).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID)).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_EVENT)).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(mockShipment);
        doThrow(new RuntimeException("Processing failed")).when(shipmentProcessor).processShipment(mockShipment, headers);

        // Act & Assert
        KafkaConsumerException exception = assertThrows(KafkaConsumerException.class, () -> {
            shipmentConsumer.onMessage("outbound-event", consumerRecord);
        });

        assertEquals("Processing failed", exception.getMessage());
        verify(headerValidator).validateAndGetHeaderMap(consumerRecord);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_EVENT);
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verify(shipmentProcessor).processShipment(mockShipment, headers);
    }

    @Test
    void testOnMessage_WithNullShipment_ShouldStillProcessCorrectly() throws KafkaConsumerException {
        // Arrange
        when(headerValidator.validateAndGetHeaderMap(consumerRecord)).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID)).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, HeaderConstants.HEADER_EVENT)).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(null);

        // Act
        Integer result = shipmentConsumer.onMessage("outbound-event", consumerRecord);

        // Assert
        assertEquals(0, result);
        verify(headerValidator).validateAndGetHeaderMap(consumerRecord);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_SHIPMENT_ID);
        verify(shipmentUtil).getHeaderValue(headers, HeaderConstants.HEADER_EVENT);
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verify(shipmentProcessor).processShipment(null, headers);
    }

    @Test
    void testOnMessage_WithEmptyHeaders_ShouldStillProcess() throws KafkaConsumerException {
        // Arrange
        Map<String, String> emptyHeaders = new HashMap<>();
        emptyHeaders.put(HeaderConstants.HEADER_SHIPMENT_ID, "12345");
        emptyHeaders.put(HeaderConstants.HEADER_EVENT, ShipmentEvent.CREATE_EVENT.getEventName());
        
        when(headerValidator.validateAndGetHeaderMap(consumerRecord)).thenReturn(emptyHeaders);
        when(shipmentUtil.getHeaderValue(emptyHeaders, HeaderConstants.HEADER_SHIPMENT_ID)).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(emptyHeaders, HeaderConstants.HEADER_EVENT)).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(mockShipment);

        // Act
        Integer result = shipmentConsumer.onMessage("outbound-event", consumerRecord);

        // Assert
        assertEquals(0, result);
        verify(headerValidator).validateAndGetHeaderMap(consumerRecord);
        verify(shipmentUtil).getHeaderValue(emptyHeaders, HeaderConstants.HEADER_SHIPMENT_ID);
        verify(shipmentUtil).getHeaderValue(emptyHeaders, HeaderConstants.HEADER_EVENT);
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verify(shipmentProcessor).processShipment(mockShipment, emptyHeaders);
    }

    @Test
    void testShipmentConsumer_ShouldImplementCorrectInterface() {
        // Assert
        assertTrue(shipmentConsumer instanceof com.cvshealth.digital.framework.service.kafka.consumer.DigitalKafkaConsumerRecordProcessor);
    }

    @Test
    void testShipmentConsumer_ShouldHaveCorrectAnnotations() {
        // Assert
        assertTrue(ShipmentConsumer.class.isAnnotationPresent(org.springframework.stereotype.Component.class));
        assertTrue(ShipmentConsumer.class.isAnnotationPresent(lombok.extern.slf4j.Slf4j.class));
        assertTrue(ShipmentConsumer.class.isAnnotationPresent(lombok.RequiredArgsConstructor.class));
    }
}
