package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer;

import com.cvshealth.digital.framework.service.kafka.exception.KafkaConsumerException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.ShipmentEvent;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentDetailsService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentProcessor;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.ShipmentUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ShipmentConsumerTest {

    @Mock
    private HeaderValidator headerValidator;

    @Mock
    private ShipmentProcessor shipmentProcessor;

    @Mock
    private ShipmentDetailsService shipmentDetailsService;

    @Mock
    private ShipmentUtil shipmentUtil;

    @InjectMocks
    private ShipmentConsumer shipmentConsumer;

    private ConsumerRecord<String, byte[]> consumerRecord;
    private Shipment mockShipment;
    private Map<String, String> headers;

    @BeforeEach
    void setUp() {
        // Setup mock shipment
        mockShipment = new Shipment();
        mockShipment.setShipmentId(12345L);
        mockShipment.setShipmentNo("SH-12345");

        // Setup headers
        headers = new HashMap<>();
        headers.put("shipment-id", "12345");
        headers.put("event", ShipmentEvent.CREATE_EVENT.getEventName());
        headers.put("lob", "specialty");

        // Setup consumer record
        Headers kafkaHeaders = new RecordHeaders();
        kafkaHeaders.add(new RecordHeader("shipment-id", "12345".getBytes()));
        kafkaHeaders.add(new RecordHeader("event", ShipmentEvent.CREATE_EVENT.getEventName().getBytes()));
        kafkaHeaders.add(new RecordHeader("lob", "specialty".getBytes()));

        consumerRecord = new ConsumerRecord<>("test-topic", 0, 0L, "key", "payload".getBytes());
        consumerRecord.headers().add("shipment-id", "12345".getBytes());
        consumerRecord.headers().add("event", ShipmentEvent.CREATE_EVENT.getEventName().getBytes());
        consumerRecord.headers().add("lob", "specialty".getBytes());
    }

    @Test
    void onMessage_withCreateEvent_shouldProcessShipment() {
        // Arrange
        when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, "shipment-id")).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, "event")).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(mockShipment);

        // Act
        shipmentConsumer.onMessage(consumerRecord);

        // Assert
        verify(headerValidator).validateHeaders(consumerRecord);
        verify(shipmentUtil).getHeaderValue(headers, "shipment-id");
        verify(shipmentUtil).getHeaderValue(headers, "event");
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verify(shipmentProcessor).processShipment(mockShipment, headers);
        verify(shipmentProcessor, never()).cancelShipment(any(), any());
    }

    @Test
    void onMessage_withCancelEvent_shouldCancelShipment() {
        // Arrange
        headers.put("event", ShipmentEvent.CANCEL_REQUEST_EVENT.getEventName());
        when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, "shipment-id")).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, "event")).thenReturn(ShipmentEvent.CANCEL_REQUEST_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(mockShipment);

        // Act
        shipmentConsumer.onMessage(consumerRecord);

        // Assert
        verify(headerValidator).validateHeaders(consumerRecord);
        verify(shipmentUtil).getHeaderValue(headers, "shipment-id");
        verify(shipmentUtil).getHeaderValue(headers, "event");
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verify(shipmentProcessor).cancelShipment(mockShipment, headers);
        verify(shipmentProcessor, never()).processShipment(any(), any());
    }

    @Test
    void onMessage_withInvalidEvent_shouldLogError() {
        // Arrange
        headers.put("event", "INVALID_EVENT");
        when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, "shipment-id")).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, "event")).thenReturn("INVALID_EVENT");
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(mockShipment);

        // Act
        shipmentConsumer.onMessage(consumerRecord);

        // Assert
        verify(headerValidator).validateHeaders(consumerRecord);
        verify(shipmentUtil).getHeaderValue(headers, "shipment-id");
        verify(shipmentUtil).getHeaderValue(headers, "event");
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verify(shipmentProcessor, never()).processShipment(any(), any());
        verify(shipmentProcessor, never()).cancelShipment(any(), any());
    }

    @Test
    void onMessage_withShipmentDetailsServiceException_shouldThrowKafkaConsumerException() {
        // Arrange
        when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, "shipment-id")).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, "event")).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenThrow(new RuntimeException("Service error"));

        // Act & Assert
        KafkaConsumerException exception = assertThrows(KafkaConsumerException.class, () -> {
            shipmentConsumer.onMessage(consumerRecord);
        });

        assertTrue(exception.getMessage().contains("Error processing shipment message"));
        verify(shipmentDetailsService).getShipmentDetails("12345");
        verify(shipmentProcessor, never()).processShipment(any(), any());
        verify(shipmentProcessor, never()).cancelShipment(any(), any());
    }

    @Test
    void onMessage_withProcessorException_shouldThrowKafkaConsumerException() {
        // Arrange
        when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, "shipment-id")).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, "event")).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(mockShipment);
        doThrow(new RuntimeException("Processor error")).when(shipmentProcessor).processShipment(mockShipment, headers);

        // Act & Assert
        KafkaConsumerException exception = assertThrows(KafkaConsumerException.class, () -> {
            shipmentConsumer.onMessage(consumerRecord);
        });

        assertTrue(exception.getMessage().contains("Error processing shipment message"));
        verify(shipmentProcessor).processShipment(mockShipment, headers);
    }

    @Test
    void onMessage_withException_shouldThrowKafkaConsumerException() {
        // Arrange
        when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenThrow(new RuntimeException("Header validation error"));

        // Act & Assert
        KafkaConsumerException exception = assertThrows(KafkaConsumerException.class, () -> {
            shipmentConsumer.onMessage(consumerRecord);
        });

        assertTrue(exception.getMessage().contains("Error processing shipment message"));
        verify(headerValidator).validateHeaders(consumerRecord);
        verify(shipmentDetailsService, never()).getShipmentDetails(any());
        verify(shipmentProcessor, never()).processShipment(any(), any());
        verify(shipmentProcessor, never()).cancelShipment(any(), any());
    }

    @Test
    void onMessage_withNullShipment_shouldThrowKafkaConsumerException() {
        // Arrange
        when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, "shipment-id")).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, "event")).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(null);

        // Act & Assert
        KafkaConsumerException exception = assertThrows(KafkaConsumerException.class, () -> {
            shipmentConsumer.onMessage(consumerRecord);
        });

        assertTrue(exception.getMessage().contains("Error processing shipment message"));
        verify(shipmentDetailsService).getShipmentDetails("12345");
    }

    @Test
    void onMessage_withEmptyShipmentId_shouldThrowKafkaConsumerException() {
        // Arrange
        when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, "shipment-id")).thenReturn("");
        when(shipmentUtil.getHeaderValue(headers, "event")).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());

        // Act & Assert
        KafkaConsumerException exception = assertThrows(KafkaConsumerException.class, () -> {
            shipmentConsumer.onMessage(consumerRecord);
        });

        assertTrue(exception.getMessage().contains("Error processing shipment message"));
        verify(shipmentDetailsService, never()).getShipmentDetails(any());
    }

    @Test
    void onMessage_withNullEvent_shouldThrowKafkaConsumerException() {
        // Arrange
        when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, "shipment-id")).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, "event")).thenReturn(null);

        // Act & Assert
        KafkaConsumerException exception = assertThrows(KafkaConsumerException.class, () -> {
            shipmentConsumer.onMessage(consumerRecord);
        });

        assertTrue(exception.getMessage().contains("Error processing shipment message"));
        verify(shipmentDetailsService, never()).getShipmentDetails(any());
    }

    @Test
    void onMessage_verifyMethodCallOrder() {
        // Arrange
        when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenReturn(headers);
        when(shipmentUtil.getHeaderValue(headers, "shipment-id")).thenReturn("12345");
        when(shipmentUtil.getHeaderValue(headers, "event")).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
        when(shipmentDetailsService.getShipmentDetails("12345")).thenReturn(mockShipment);

        // Act
        shipmentConsumer.onMessage(consumerRecord);

        // Assert - verify the order of method calls
        var inOrder = inOrder(headerValidator, shipmentUtil, shipmentDetailsService, shipmentProcessor);
        inOrder.verify(headerValidator).validateHeaders(consumerRecord);
        inOrder.verify(shipmentUtil).getHeaderValue(headers, "shipment-id");
        inOrder.verify(shipmentUtil).getHeaderValue(headers, "event");
        inOrder.verify(shipmentDetailsService).getShipmentDetails("12345");
        inOrder.verify(shipmentProcessor).processShipment(mockShipment, headers);
    }

    @Test
    void onMessage_withDifferentShipmentIds_shouldProcessCorrectly() {
        // Arrange
        String[] shipmentIds = {"12345", "67890", "11111"};
        
        for (String shipmentId : shipmentIds) {
            headers.put("shipment-id", shipmentId);
            Shipment shipment = new Shipment();
            shipment.setShipmentId(Long.parseLong(shipmentId));
            
            when(headerValidator.validateHeaders(any(ConsumerRecord.class))).thenReturn(headers);
            when(shipmentUtil.getHeaderValue(headers, "shipment-id")).thenReturn(shipmentId);
            when(shipmentUtil.getHeaderValue(headers, "event")).thenReturn(ShipmentEvent.CREATE_EVENT.getEventName());
            when(shipmentDetailsService.getShipmentDetails(shipmentId)).thenReturn(shipment);

            // Act
            shipmentConsumer.onMessage(consumerRecord);

            // Assert
            verify(shipmentDetailsService).getShipmentDetails(shipmentId);
            verify(shipmentProcessor).processShipment(shipment, headers);
        }
    }
}
