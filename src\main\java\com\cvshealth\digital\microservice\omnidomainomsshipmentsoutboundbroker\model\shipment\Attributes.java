package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Attributes {
    private String pickComments;
    private String packerInstructions;
    private String bundleInstructions;
    private boolean isAdditionalRxLabel;
    private boolean isSpecialtyAtRetailOrder;
    private String groupNumber;
    private String preferredLanguage;
    private String rxLabelLanguage;
    private boolean isSafetyCapRequired;
}
