package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentExtn {

    private String paymentPreference;
    private double orderSubTotal;
    private String signatureFlag;
    private String shipHandlingInstruction;
    private String shipMethodIndicator;
    private List<ShipmentPaymentInfo> paymentInfo;
    private double shippingCharge;
    private double orderTotal;
    private double shippingTax;
    private String planPriority;
    private String esignature;
    private String otchsMemberId;
    private String otchsOrderNo;
    private String otchsShipInstructionType;
    private String otchsplan;
    private String otchsPhoneNumber;
}