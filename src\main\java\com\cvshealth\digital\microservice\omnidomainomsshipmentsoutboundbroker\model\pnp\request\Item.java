
package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Item {

    private String itemDescription;
    private Integer quantityRequested;
    private Double lineTotalWithoutTax;
    private Double unitPrice;
    private Double originalUnitPrice;
    private String itemId;
    private String itemType;
    private String fromstore;
    private String tostore;
    private String rxNDC;
    private String hazmatFlag;
    private String deliveryMode;
    private String unitOfMeasure;
    private String upcNumber;
    private String primeLineNumber;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String[] alternateUpcs;
    private String fillNumber;
    private String fillSeqNum;
    private String fillVerNum;
    private String rxNumber;
    private String patientID;
    private String substitutionFlag;
    private String patientType;
    private String patientFirstName;
    private String patientLastName;
    private String unitWeight;
    private String substitutionItemList;
    private String fsaFlag;

}
