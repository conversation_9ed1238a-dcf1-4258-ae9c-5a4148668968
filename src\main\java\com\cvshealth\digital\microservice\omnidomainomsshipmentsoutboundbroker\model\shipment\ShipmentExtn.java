package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentExtn {

    private String eSignature;
    private String paymentPreference;
    private String orderSubTotal;
    private String signatureFlag;
    private String shipHandlingInstruction;
    private String shipMethodIndicator;
    //private List<PaymentInfo> paymentInfo;
    private String shippingCharge;
    private String shippingTax;
    private String planPriority;
    private String planId;
    private String otchsOrderNo;
    private String cncOrderId;
    private String orderTotal;
    private String idFlag;
    private String otchsMemberId;
    private String otchsFlexCardType;

}
