package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client;

import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.exception.OMSShipmentBrokerOutboundServiceException;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import io.github.resilience4j.retry.annotation.Retry;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

@Component
public class RestApiClient {

    @Value("${shipment.get-by-id-url}")
    String getShipmentApiUrl;

    private final RestClient restClient;



    public RestApiClient(@Qualifier("restClient") RestClient restClient) {
        this.restClient = restClient;
    }

    @Retry(name="fetch-shipment-details", fallbackMethod = "fetchShipmentDetailsFallback")
    public Shipment getShipmentDetails(String shipmentId) {

        String url = UriComponentsBuilder
                .fromHttpUrl(getShipmentApiUrl)
                .pathSegment(shipmentId)
                .build()
                .toUriString();

        return restClient.get()
                .uri(url)
                .retrieve()
                .onStatus(HttpStatusCode::isError, (request, response) -> {
                    throw  new OMSShipmentBrokerOutboundServiceException(response.getStatusText(),response.getStatusCode(), shipmentId);
                })
                .body(Shipment.class);
    }

    public Shipment fetchShipmentDetailsFallback(String shipmentId, OMSShipmentBrokerOutboundServiceException ex) {
        CvsLogger.error(ex.getShipmentId() + "-" + ex.getMessage(), ex);
        throw ex;
    }

}