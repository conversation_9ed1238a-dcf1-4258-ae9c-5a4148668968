package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ShipmentEvent {

    CREATE_EVENT("CREATE_EVENT", "100", "Shipment create event"),
    FULFILLMENT_ACK_EVENT("FULFILLMENT_ACK_EVENT", "101", "Fulfillment Acknowledged event"),
    SHIPMENT_ACK_EVENT("SHIPMENT_ACK_EVENT", "102", "Shipment Acknowledged event"),
    PACKED_EVENT("PACKED_EVENT", "103", "Shipment packed event"),
    SHIP_LABEL_EVENT("SHIP_LABEL_EVENT", "104", "Shipment ship label event"),
    SHIPPED_EVENT("SHIPPED_EVENT", "105", "Shipment shipped event"),
    DELIVERED_EVENT("DELIVERED_EVENT", "106", "Shipment delivered event"),
    CANCELLED_EVENT("CANCELLED_EVENT", "107", "Shipment cancelled event"),
    FULFILLMENT_NACK_EVENT("FULFILLMENT_NACK_EVENT", "108", "Fulfillment not Acknowledged event"),
    SHIPMENT_NACK_EVENT("SHIPMENT_NACK_EVENT", "109", "Shipment not Acknowledged event"),
    CANCEL_REQUEST_EVENT("CANCEL_REQUEST_EVENT", "110", "Shipment cancel request event"),
    CANCEL_ACK_EVENT("CANCEL_ACK_EVENT", "111", "Shipment cancel Acknowledged event"),
    CANCEL_NACK_EVENT("CANCEL_NACK_EVENT", "112", "Shipment cancel not Acknowledged event"),
    CANCEL_REJECTED_EVENT("CANCEL_REJECTED_EVENT", "113", "Cancel rejected event"),
    PRINT_PACK_EVENT("PRINT_PACK_EVENT", "114", "Print pack request event");

    private final String eventName;
    private final String eventCode;
    private final String eventDescription;

    private static final Map<String, ShipmentEvent> shipmentEventMap = new HashMap<>();

    static {
        for (ShipmentEvent e : ShipmentEvent.values()) {
            if (shipmentEventMap.put(e.getEventCode(), e) != null) {
                throw new IllegalArgumentException("invalid event code: " + e.getEventCode());
            }
        }
    }

    public static ShipmentEvent getEvent(String code) {
        return shipmentEventMap.get(code);
    }

}
