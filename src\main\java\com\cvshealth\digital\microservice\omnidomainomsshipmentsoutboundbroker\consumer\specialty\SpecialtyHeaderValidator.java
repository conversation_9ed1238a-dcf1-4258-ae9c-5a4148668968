package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.specialty;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.HeaderConstants;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.HeaderValidator;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.ShipmentUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.List;
import java.util.Map;

public class SpecialtyHeaderValidator implements HeaderValidator  {

    private static final List<String> requiredHeaders = List.of(HeaderConstants.HEADER_LOB,
                                                                HeaderConstants.HEADER_EVENT,
                                                                HeaderConstants.HEADER_SHIPMENT_NUMBER,
                                                                HeaderConstants.HEADER_SHIPMENT_ID,
                                                                HeaderConstants.HEADER_EVENT_SOURCE,
                                                                HeaderConstants.HEADER_EVENT_TS);

    private final ShipmentUtil shipmentUtil;

    public SpecialtyHeaderValidator(ShipmentUtil shipmentUtil) {
        this.shipmentUtil = shipmentUtil;
    }

    @Override
    public Map<String, String> validateAndGetHeaderMap(ConsumerRecord<String, byte[]> consumerRecord) {
        return shipmentUtil.validateAndGetHeaderMap(consumerRecord, requiredHeaders);
    }
}
