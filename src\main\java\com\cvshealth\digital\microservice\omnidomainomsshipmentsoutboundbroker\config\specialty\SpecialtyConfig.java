package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.HeaderValidator;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.specialty.SpecialtyHeaderValidator;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.producer.EventPublisherService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.specialty.ClientService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentProcessor;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.impl.SpecialtyShipmentProcessorImpl;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.ShipmentUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.client.RestClient;


@Configuration
@Profile("specialty")
public class SpecialtyConfig {

    @Qualifier("restClient")
    private final RestClient restClient;

    private final ExternalEntityRestClientCache externalEntityRestClientCache;

    public SpecialtyConfig(RestClient restClient, ExternalEntityRestClientCache externalEntityRestClientCache) {
        this.restClient = restClient;
        this.externalEntityRestClientCache = externalEntityRestClientCache;
    }

    @Bean
    public ShipmentProcessor getShipmentProcessor(ClientService clientService, ObjectMapper objectMapper) {
        return new SpecialtyShipmentProcessorImpl(clientService, objectMapper);
    }

    @Bean
    public ClientService getClientService(ClientProperties clientProperties, EventPublisherService eventPublisherService, ObjectMapper objectMapper) {
        return new ClientService(objectMapper, clientProperties, eventPublisherService, restClient, externalEntityRestClientCache);
    }

    @Bean
    public HeaderValidator getHeaderValidator(ShipmentUtil shipmentUtil) {
        return new SpecialtyHeaderValidator(shipmentUtil);
    }
}
