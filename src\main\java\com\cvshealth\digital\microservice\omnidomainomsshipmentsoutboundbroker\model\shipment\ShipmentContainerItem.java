package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.DatabaseProgId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentContainerItem {

    private long shipmentContainerItemId;
    private long shipmentContainerId;
    private long shipmentId;
    private long shipmentItemId;
    private String itemId;
    private int quantity;
    private int orderedQuantity;
    private Instant creationTimestamp;
    private Instant lastModifiedTimestamp;
    private DatabaseProgId createProgId;
    private DatabaseProgId modifyProgId;

}
