package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.context.annotation.Profile;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Profile("specialty")
public class ClientConfig {

    private String clientName;
    private String tokenUrl;
    private String grantType;
    private String audience;
    private List<String> scopes;
    private String clientId;
    private String clientSecret;
    private String clientTrustStore;
    private String clientTrustStorePassword;
    private String createShipmentUrl;
    private String cancelShipmentUrl;
    //TODO added for testing to bypass external call, will make it true in QA for BOB client
    private boolean testFlag = false;
}
