package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.DatabaseProgId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentAddress {

    private long shipmentAddressId;
    private long shipmentId;
    private String firstName;
    private String middleName;
    private String lastName;
    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
    private String city;
    private String state;
    private String zipCode;
    private String country;
    private String dayPhone;
    private String cellPhone;
    private String preferredContactPhone;
    private String emailId;
    private String shortZipCode;
    private String addressType;
    private Instant creationTimestamp;
    private Instant lastModifiedTimestamp;
    private DatabaseProgId createProgId;
    private DatabaseProgId modifyProgId;

}
