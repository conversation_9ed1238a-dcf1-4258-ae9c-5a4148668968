package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.Instant;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Rx {
    private String patientId;
    private String rxNumber;
    private String drugProductId;
    private Double quantityRequired;
    private Double refillMsg;
    private String fillNumber;
    private String ndcNum;
    private String custDrugId;
    private boolean safetyCap;
    private String drugDesc;
    private String brandName;
    private String mfrCode;
    private String unitOfMeasure;
    private Instant discardDate;
    private String brandGenericText;
    private String instruction;
    private String drFirstname;
    private String drMiddleName;
    private String drLastname;
    private String drType;
    private String drDeaNum;
    private String custLangCode;
    private String lotNumber;
    private String pv1Id;
    private int dispensedQuantity;
    private String uom;
    private int daysSupply;
    private boolean longSigFlag;
    private String rxAuxMessage;
    private String itemGenericName;
}
