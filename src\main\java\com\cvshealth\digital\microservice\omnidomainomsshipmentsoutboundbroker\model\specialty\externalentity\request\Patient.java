package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Patient {
    private String patientId;
    private String firstName;
    private String middleInitial;
    private String lastName;
    private String addressLine1;
    private String addressLine2;
    private String city;
    private String state;
    private String zip;
    private String zipSuffix;
    private int rxCount;
    private List<Rx> rx;
}
