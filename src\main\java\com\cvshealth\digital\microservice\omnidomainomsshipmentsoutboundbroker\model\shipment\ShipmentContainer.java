package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;


import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.DatabaseProgId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentContainer {

    private long shipmentContainerId;
    private long shipmentId;
    private String trackingNo;
    private String scac;
    private Instant shipDate;
    private String carrierServiceCode;
    private boolean isHazmat;
    private Instant creationTimestamp;
    private Instant lastModifiedTimestamp;
    private DatabaseProgId createProgId;
    private DatabaseProgId modifyProgId;
}
