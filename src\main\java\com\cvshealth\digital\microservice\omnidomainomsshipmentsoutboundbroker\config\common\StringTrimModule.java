package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.common;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdScalarDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.Serial;

public class StringTrimModule extends SimpleModule {

    @Serial
    private static final long serialVersionUID = 1L;

    public StringTrimModule() {
        addDeserializer(String.class, new StdScalarDeserializer<>(String.class) {
            @Serial
            private static final long serialVersionUID = 1L;

            @Override
            public String deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
                return StringUtils.trim(jp.getValueAsString());
            }
        });
    }
}
