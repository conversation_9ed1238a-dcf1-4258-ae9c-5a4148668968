package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery;

import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.framework.service.logging.service.LogServiceContext;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.*;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.rxdelivery.RxDeliveryRestApiClient;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;

import static com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.CVSConstants.ERROR_SERVERITY_CRITICAL;
import static com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.CVSConstants.TAG_ERROR_SERVERITY;


public class GetShipmentDetailsService {

    private final RxDeliveryRestApiClient restApiClient;

    public GetShipmentDetailsService(RxDeliveryRestApiClient restApiClient) {
        this.restApiClient = restApiClient;
    }

    public OMSFetchShipmentResponse getShipmentDetails(ShipmentRequest shipment) {

        OMSFetchShipmentResponse response = new OMSFetchShipmentResponse();

        try {
            Shipment foundShipment = restApiClient.invokeShipmentDetails(shipment.getShipmentId());

            response.setShipmentDetails(mapShipmentDetails(foundShipment));

            return response;
        } catch(Exception e) {
            LogServiceContext.addTags(TAG_ERROR_SERVERITY, ERROR_SERVERITY_CRITICAL);
            CvsLogger.error("Error fetching shipment details", e);
            throw e;
        }
    }

    private ShipmentDetails mapShipmentDetails(Shipment shipment) {
        if (shipment == null) {
            return null;
        }

        ShipmentDetails details = new ShipmentDetails();

        details.setShipmentId(String.valueOf(shipment.getShipmentId()));
        details.setShipmentNo(shipment.getShipmentNo());
        details.setShipNode(shipment.getShipNode());
        details.setDeliveryCode(shipment.getDeliveryCode());
        details.setShipmentStatus(shipment.getShipmentStatus() != null ? shipment.getShipmentStatus().name() : null);
        details.setOrderNo(shipment.getOrderNo());
        details.setCustomerProfileId(shipment.getCustomerProfileId());
        details.setCustomerProfileType(shipment.getCustomerProfileType());
        details.setOrderType(shipment.getOrderType());
        details.setEntryType(shipment.getEntryType());
        details.setDeliveryMethod(shipment.getDeliveryMethod());
        details.setStorePickupLocation(shipment.getStorePickupLocation());
        details.setShipNodeLocale(shipment.getShipNodeLocale());
        details.setShipmentType(shipment.getShipmentType());
        details.setOrderChannel(shipment.getOrderChannel());
        details.setExtraCareCardNo(shipment.getExtraCareCardNo());
        details.setCarepassOrder(shipment.isCarepassOrder());

        if (shipment.getOrderDate() != null) {
            details.setOrderDate(shipment.getOrderDate().toString());
        }

        if (shipment.getShipDate() != null) {
            details.setCreationTimestamp(shipment.getShipDate().toString());
        }

        if (shipment.getStoreSlaDate() != null) {
            details.setStoreSlaDate(shipment.getStoreSlaDate().toString());
        }

        if (shipment.getCustomerSlaDate() != null) {
            details.setCustomerSlaDate(shipment.getCustomerSlaDate().toString());
        }

        if (shipment.getShipmentAddressList() != null) {
            details.setShipmentAddresses(shipment.getShipmentAddressList().stream()
                    .map(address -> {
                        ShipmentAddress shipmentAddress = new ShipmentAddress();
                        shipmentAddress.setAddressLine1(address.getAddressLine1());
                        shipmentAddress.setAddressLine2(address.getAddressLine2());
                        shipmentAddress.setCity(address.getCity());
                        shipmentAddress.setState(address.getState());
                        shipmentAddress.setCountry(address.getCountry());
                        shipmentAddress.setZipCode(address.getZipCode());
                        shipmentAddress.setAddressType(address.getAddressType());
                        shipmentAddress.setFirstName(address.getFirstName());
                        shipmentAddress.setMiddleName(address.getMiddleName());
                        shipmentAddress.setLastName(address.getLastName());
                        shipmentAddress.setEmailId(address.getEmailId());
                        shipmentAddress.setDayPhone(address.getDayPhone());
                        shipmentAddress.setPreferredContactPhone(address.getPreferredContactPhone());
                        return shipmentAddress;
                    }).toList());
        }

        if (shipment.getShipmentContainerList() != null) {
            details.setShipmentContainers(shipment.getShipmentContainerList().stream()
                    .map(container -> {
                        ShipmentContainer shipmentContainer = new ShipmentContainer();
                        shipmentContainer.setCarrierServiceCode(container.getCarrierServiceCode());
                        shipmentContainer.setTrackingNo(container.getTrackingNo());
                        return shipmentContainer;
                    }).toList());
        }

        if (shipment.getShipmentExtn() != null) {
            ShipmentExtn shipmentExtn = getShipmentExtn(shipment);
            details.setShipmentExtn(shipmentExtn);
        }

        if (shipment.getShipmentItemList() != null) {
            details.setShipmentItems(shipment.getShipmentItemList().stream().map(this::getShipmentItem).toList());
        }

        return details;
    }

    private ShipmentItem getShipmentItem(com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.ShipmentItem item) {
        ShipmentItem shipmentItem = new ShipmentItem();
        shipmentItem.setShipmentItemId(Math.toIntExact(item.getShipmentItemId()));
        shipmentItem.setShipmentId(Math.toIntExact(item.getShipmentId()));
        shipmentItem.setShipToStoreNo(item.getShipToStoreNo());
        shipmentItem.setShipmentItemNo(item.getShipmentItemNo());
        shipmentItem.setOrderLineNo(String.valueOf(item.getOrderLineNo()));
        shipmentItem.setItemId(item.getItemId());
        shipmentItem.setUnitWeight(String.valueOf(item.getUnitWeight()));
        if (item.getNetWeight() != null) {
            shipmentItem.setNetWeight(item.getNetWeight());
        }
        shipmentItem.setQuantity(item.getQuantity());
        shipmentItem.setOrderedQuantity(item.getOrderedQuantity());
        shipmentItem.setItemDesc(item.getItemDesc());
        shipmentItem.setShortedQty(item.getShortedQty());
        shipmentItem.setLineCancellationReason(item.getLineCancellationReason());
        shipmentItem.setCreationTimestamp(String.valueOf(item.getCreationTimestamp()));
        shipmentItem.setLastModifiedTimestamp(String.valueOf(item.getLastModifiedTimestamp()));
        if (item.getLineTotalWithoutTax() != null) {
            shipmentItem.setLineTotalWithoutTax(item.getLineTotalWithoutTax());
        }
        if (item.getUnitPrice() != null) {
            shipmentItem.setUnitPrice(item.getUnitPrice());
        }
        if (item.getOriginalUnitPrice() != null) {
            shipmentItem.setOriginalUnitPrice(item.getOriginalUnitPrice());
        }
        shipmentItem.setLineTax(0.0);
        shipmentItem.setFillNumber(item.getFillNumber());
        shipmentItem.setFillSeqNum(item.getFillSeqNum());
        shipmentItem.setFillVerNum(item.getFillVerNum());
        shipmentItem.setRxNumber(item.getRxNumber());
        shipmentItem.setMemberId(null);
        shipmentItem.setRxStatus(item.getRxStatus());
        shipmentItem.setItemType(item.getItemType());
        shipmentItem.setUpcNumber(item.getUpcNumber());
        if (item.getAlternateUPCList() != null) {
            shipmentItem.setAlternateUpcList(item.getAlternateUPCList().split(","));
        }
        shipmentItem.setSubstitutionItemList(item.getSubstitutionItemList());
        shipmentItem.setFsaItem(item.isFSAItem());
        shipmentItem.setRxNDC(item.getNdcNumber());
        shipmentItem.setHazmat(item.isHazmat());
        ShipmentItemExtn shipmentItemExtn = getShipmentItemExtn(item);
        shipmentItem.setShipmentItemExtn(shipmentItemExtn);
        return shipmentItem;
    }

    private ShipmentItemExtn getShipmentItemExtn(com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.ShipmentItem item) {
        ShipmentItemExtn shipmentItemExtn = new ShipmentItemExtn();
        if (item.getShipmentItemExtn() != null) {
            shipmentItemExtn.setPatientFirstName(item.getShipmentItemExtn().getPatientFirstName());
            shipmentItemExtn.setPatientLastName(item.getShipmentItemExtn().getPatientLastName());
            shipmentItemExtn.setRxNDC(item.getShipmentItemExtn().getRxNDC());
            shipmentItemExtn.setPatientId(item.getShipmentItemExtn().getPatientId());
            shipmentItemExtn.setPatientType(item.getShipmentItemExtn().getPatientType());
        }
        return shipmentItemExtn;
    }

    private ShipmentExtn getShipmentExtn(Shipment shipment) {
        ShipmentExtn shipmentExtn = new ShipmentExtn();

        shipmentExtn.setPaymentPreference(shipment.getShipmentExtn().getPaymentPreference());
        shipmentExtn.setSignatureFlag(shipment.getShipmentExtn().getSignatureFlag());
        shipmentExtn.setShipHandlingInstruction(shipment.getShipmentExtn().getShipHandlingInstruction());
        shipmentExtn.setShipMethodIndicator(shipment.getShipmentExtn().getShipMethodIndicator());
        shipmentExtn.setPlanPriority(shipment.getShipmentExtn().getPlanPriority());
        shipmentExtn.setEsignature(shipment.getShipmentExtn().getESignature());
        shipmentExtn.setOtchsMemberId(shipment.getShipmentExtn().getOtchsMemberId());
        shipmentExtn.setOtchsOrderNo(shipment.getShipmentExtn().getOtchsOrderNo());

        if (shipment.getShipmentExtn().getShippingCharge() != null) {
            shipmentExtn.setShippingCharge(getStringAsDouble(shipment.getShipmentExtn().getShippingCharge()));
        } else {
            shipmentExtn.setShippingCharge(0.0);
        }

        if (shipment.getShipmentExtn().getShippingTax() != null) {
            shipmentExtn.setShippingTax(getStringAsDouble(shipment.getShipmentExtn().getShippingTax()));
        } else {
            shipmentExtn.setShippingTax(0.0);
        }

        shipmentExtn.setOtchsShipInstructionType(null);
        shipmentExtn.setOtchsplan(null);
        shipmentExtn.setOtchsPhoneNumber(null);

        if (shipment.getShipmentExtn().getOrderSubTotal() != null) {
            shipmentExtn.setOrderSubTotal(getStringAsDouble(shipment.getShipmentExtn().getOrderSubTotal()));
        } else {
            shipmentExtn.setOrderSubTotal(0.0);
        }

        return shipmentExtn;
    }

    private double getStringAsDouble(String value) {
        if (value == null || value.isEmpty()) {
            return 0.0;
        }
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            CvsLogger.error("Error parsing double from string: " + value, e);
            return 0.0;
        }
    }
}
