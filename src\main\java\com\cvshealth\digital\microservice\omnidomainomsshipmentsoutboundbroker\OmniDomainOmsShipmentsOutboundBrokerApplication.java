package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * omni-domain-oms-shipments-outbound-broker application.
 *
 * <AUTHOR> Shah
 */
@ComponentScan("com.cvshealth.*")
@SpringBootApplication
@OpenAPIDefinition(info = @Info(title = "${info.app.name}", version = "${info.app.version}", description = "${info.app.description}"))
public class OmniDomainOmsShipmentsOutboundBrokerApplication {

    public static void main(String[] args) {
        SpringApplication.run(OmniDomainOmsShipmentsOutboundBrokerApplication.class, args);
    }

}
