package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.exception;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatusCode;

@Data
@NoArgsConstructor
public class OMSShipmentBrokerOutboundServiceException extends RuntimeException {
    private int statusCode;
    private String shipmentId;
    private String strStoreNum;
    private String message;
    private String errorCode;

    public OMSShipmentBrokerOutboundServiceException(String message, HttpStatusCode statusCode, String shipmentId) {
        this.message=message;
        this.statusCode = statusCode.value();
        this.shipmentId = shipmentId;
    }

    public OMSShipmentBrokerOutboundServiceException(String statusText, int statusCode) {
        this.statusCode = statusCode;
        this.message = statusText;
    }
}