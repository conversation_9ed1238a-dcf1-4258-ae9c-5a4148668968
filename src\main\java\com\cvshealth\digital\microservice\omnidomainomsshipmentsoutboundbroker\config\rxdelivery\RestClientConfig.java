package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.rxdelivery;

import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.io.HttpClientConnectionManager;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.io.SocketConfig;
import org.apache.hc.core5.util.Timeout;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClient;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.Base64;

@Profile("rxdelivery")
@Configuration
public class RestClientConfig {

    @Value("${rest-client.ssl.enabled:true}")
    boolean restClientSslEnabled;

    @Value("${pnp.trust-store}")
    String pnpTrustStore;

    @Value("${pnp.trust-store-password}")
    String pnpTrustStorePwd;

    @Value("${pnp.timeout}")
    int pnpTimeOut;

    @Bean(name = "restClient")
    public RestClient restClient() {
        return RestClient.builder()
                .requestFactory(getClientHttpRequestFactoryWithoutSsl())
                .build();
    }

    private HttpComponentsClientHttpRequestFactory getClientHttpRequestFactoryWithoutSsl() {

        Timeout timeout = Timeout.ofSeconds(pnpTimeOut);
        HttpClientConnectionManager cm = PoolingHttpClientConnectionManagerBuilder.create()
                .setDefaultSocketConfig(SocketConfig.custom().setSoTimeout(timeout).build())
                .setDefaultConnectionConfig(ConnectionConfig.custom().setConnectTimeout(timeout).setConnectTimeout(timeout).build())
                .build();
        CloseableHttpClient httpClient = HttpClients
                .custom()
                .setConnectionManager(cm)
                .setDefaultRequestConfig(RequestConfig.custom().setConnectionRequestTimeout(timeout).setResponseTimeout(timeout).build())
                .build();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);

        return requestFactory;
    }

    @Bean(name = "restClientWithSsl")
    public RestClient restClientWithSsl() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException, CertificateException, IOException {
        try {
            if (!restClientSslEnabled) {
                CvsLogger.info("SSL is not enabled for RestClient, using non-SSL configuration.");
                return restClient();
            }

            return RestClient.builder()
                    .requestFactory(getClientHttpRequestFactory())
                    .build();
        } catch (Exception e) {
            CvsLogger.error("Error while creating rest client with SSL", e);
            throw e;
        }
    }

    private HttpComponentsClientHttpRequestFactory getClientHttpRequestFactory() throws KeyStoreException, CertificateException, IOException, NoSuchAlgorithmException, KeyManagementException {

        KeyStore trustStore = KeyStore.getInstance("jks");
        byte[] decodedJKS = Base64.getDecoder().decode(pnpTrustStore.replaceAll("\\s", ""));
        trustStore.load(new ByteArrayInputStream(decodedJKS), pnpTrustStorePwd.toCharArray());
        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance("SunX509");
        trustManagerFactory.init(trustStore);

        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustManagerFactory.getTrustManagers(), null);
        SSLConnectionSocketFactory sslConFactory = new SSLConnectionSocketFactory(sslContext);

        Timeout timeout = Timeout.ofSeconds(pnpTimeOut);

        HttpClientConnectionManager cm = PoolingHttpClientConnectionManagerBuilder.create()
                .setDefaultSocketConfig(SocketConfig.custom().setSoTimeout(timeout).build())
                .setDefaultConnectionConfig(ConnectionConfig.custom().setConnectTimeout(timeout).setConnectTimeout(timeout).build())
                .setSSLSocketFactory(sslConFactory)
                .build();

        CloseableHttpClient httpClient = HttpClients
                .custom()
                .setConnectionManager(cm)
                .setDefaultRequestConfig(RequestConfig.custom().setConnectionRequestTimeout(timeout).setResponseTimeout(timeout).build())
                .build();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        return requestFactory;
    }
}
