package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.RestClient;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.Arrays;
import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;

class ExternalEntityRestClientCacheTest {

    private ExternalEntityRestClientCache cache;
    private ClientConfig clientConfig;

    @BeforeEach
    void setUp() {
        cache = new ExternalEntityRestClientCache();
        clientConfig = createTestClientConfig();
    }

    @Test
    void getExternalEntityCachedRestClient_withValidConfig_shouldReturnRestClient() {
        // Act
        RestClient restClient = cache.getExternalEntityCachedRestClient(clientConfig);

        // Assert
        assertNotNull(restClient);
    }

    @Test
    void getExternalEntityCachedRestClient_withSameClientName_shouldReturnCachedInstance() {
        // Act
        RestClient restClient1 = cache.getExternalEntityCachedRestClient(clientConfig);
        RestClient restClient2 = cache.getExternalEntityCachedRestClient(clientConfig);

        // Assert
        assertNotNull(restClient1);
        assertNotNull(restClient2);
        assertSame(restClient1, restClient2, "Should return the same cached instance");
    }

    @Test
    void getExternalEntityCachedRestClient_withDifferentClientNames_shouldReturnDifferentInstances() {
        // Arrange
        ClientConfig config1 = createTestClientConfig();
        config1.setClientName("CLIENT1");
        
        ClientConfig config2 = createTestClientConfig();
        config2.setClientName("CLIENT2");

        // Act
        RestClient restClient1 = cache.getExternalEntityCachedRestClient(config1);
        RestClient restClient2 = cache.getExternalEntityCachedRestClient(config2);

        // Assert
        assertNotNull(restClient1);
        assertNotNull(restClient2);
        assertNotSame(restClient1, restClient2, "Should return different instances for different clients");
    }

    @Test
    void getExternalEntityCachedRestClient_multipleCallsWithSameConfig_shouldUseCacheEfficiently() {
        // Act
        RestClient restClient1 = cache.getExternalEntityCachedRestClient(clientConfig);
        RestClient restClient2 = cache.getExternalEntityCachedRestClient(clientConfig);
        RestClient restClient3 = cache.getExternalEntityCachedRestClient(clientConfig);

        // Assert
        assertSame(restClient1, restClient2);
        assertSame(restClient2, restClient3);
        assertSame(restClient1, restClient3);
    }

    @Test
    void getExternalEntityCachedRestClient_withNullClientName_shouldThrowException() {
        // Arrange
        clientConfig.setClientName(null);

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            cache.getExternalEntityCachedRestClient(clientConfig);
        });
    }

    @Test
    void getExternalEntityRestClient_withoutSSL_shouldReturnRestClient() throws Exception {
        // Arrange
        ClientConfig configWithoutSSL = createTestClientConfig();
        configWithoutSSL.setClientTrustStore(null);
        configWithoutSSL.setClientTrustStorePassword(null);

        // Act
        RestClient restClient = cache.getExternalEntityRestClient(configWithoutSSL);

        // Assert
        assertNotNull(restClient);
    }

    @Test
    void getExternalEntityRestClient_withNullTrustStore_shouldReturnRestClient() throws Exception {
        // Arrange
        clientConfig.setClientTrustStore(null);

        // Act
        RestClient restClient = cache.getExternalEntityRestClient(clientConfig);

        // Assert
        assertNotNull(restClient);
    }

    @Test
    void getExternalEntityRestClient_withValidSSL_shouldReturnRestClient() throws Exception {
        // Arrange
        ClientConfig configWithSSL = createTestClientConfigWithValidSSL();

        // Act
        RestClient restClient = cache.getExternalEntityRestClient(configWithSSL);

        // Assert
        assertNotNull(restClient);
    }

    @Test
    void getExternalEntityRestClient_withEmptyTrustStore_shouldThrowException() {
        // Arrange
        clientConfig.setClientTrustStore("");

        // Act & Assert
        assertThrows(Exception.class, () -> {
            cache.getExternalEntityRestClient(clientConfig);
        });
    }

    @Test
    void getExternalEntityRestClient_withWhitespaceInTrustStore_shouldHandleCorrectly() {
        // Arrange
        String trustStoreWithWhitespace = createValidBase64TrustStore() + "   \n\t  ";
        clientConfig.setClientTrustStore(trustStoreWithWhitespace);

        // Act & Assert
        assertDoesNotThrow(() -> {
            cache.getExternalEntityRestClient(clientConfig);
        });
    }

    @Test
    void getExternalEntityRestClient_withInvalidBase64TrustStore_shouldThrowException() {
        // Arrange
        clientConfig.setClientTrustStore("invalid-base64-data!");

        // Act & Assert
        assertThrows(Exception.class, () -> {
            cache.getExternalEntityRestClient(clientConfig);
        });
    }

    @Test
    void getExternalEntityRestClient_withNullTrustStorePassword_shouldThrowException() {
        // Arrange
        clientConfig.setClientTrustStore(createValidBase64TrustStore());
        clientConfig.setClientTrustStorePassword(null);

        // Act & Assert
        assertThrows(Exception.class, () -> {
            cache.getExternalEntityRestClient(clientConfig);
        });
    }

    @Test
    void getExternalEntityRestClient_withMalformedJksData_shouldThrowException() {
        // Arrange
        String malformedJks = Base64.getEncoder().encodeToString("not-a-valid-jks-file".getBytes());
        clientConfig.setClientTrustStore(malformedJks);

        // Act & Assert
        assertThrows(Exception.class, () -> {
            cache.getExternalEntityRestClient(clientConfig);
        });
    }

    @Test
    void getExternalEntityCachedRestClient_withInvalidSSL_shouldThrowRuntimeException() {
        // Arrange
        ClientConfig invalidConfig = createTestClientConfig();
        invalidConfig.setClientTrustStore("invalid-base64");

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            cache.getExternalEntityCachedRestClient(invalidConfig);
        });

        assertTrue(exception.getMessage().contains("Failed to create rest template for client"));
        assertTrue(exception.getMessage().contains(invalidConfig.getClientName()));
    }

    /**
     * Helper method to create a basic test client configuration
     */
    private ClientConfig createTestClientConfig() {
        ClientConfig config = new ClientConfig();
        config.setClientName("TEST_CLIENT");
        config.setTokenUrl("https://auth.test.com/oauth/token");
        config.setGrantType("client_credentials");
        config.setAudience("https://api.test.com");
        config.setScopes(Arrays.asList("read", "write"));
        config.setClientId("test-client-id");
        config.setClientSecret("test-client-secret");
        config.setCreateShipmentUrl("https://api.test.com/v1/create");
        config.setCancelShipmentUrl("https://api.test.com/v1/cancel");
        config.setTestFlag(false);
        return config;
    }

    /**
     * Helper method to create a client configuration with valid SSL settings
     */
    private ClientConfig createTestClientConfigWithValidSSL() {
        ClientConfig config = createTestClientConfig();
        config.setClientTrustStore(createValidBase64TrustStore());
        config.setClientTrustStorePassword("changeit");
        return config;
    }

    /**
     * Helper method to create a valid base64 encoded trust store
     * This creates a minimal valid JKS structure for testing
     */
    private String createValidBase64TrustStore() {
        // This is a minimal valid JKS file structure encoded in base64
        // In a real scenario, this would be a proper JKS file
        byte[] minimalJks = {
            (byte)0xfe, (byte)0xed, (byte)0xfe, (byte)0xed, // JKS magic number
            0x00, 0x00, 0x00, 0x02, // version
            0x00, 0x00, 0x00, 0x00  // entry count (0)
        };
        return Base64.getEncoder().encodeToString(minimalJks);
    }
}
