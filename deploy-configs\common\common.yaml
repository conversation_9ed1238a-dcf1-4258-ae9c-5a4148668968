#app related data
app:
  name: omni-domain-oms-shipments-outbound-broker
  port: 21000
  gitRepoName: cvs-health-source-code/omni-domain-oms-shipments-outbound-broker
#  type: "java"
#  enableOpenTelemetry: "true"

#livenessProbe details
livenessProbeDetails:
  probePath: "/microservices/omni-domain-oms-shipments-outbound-broker/actuator/health/liveness"
  initialDelaySeconds: 30

#readinessProbe details
readinessProbeDetails:
  probePath: "/microservices/omni-domain-oms-shipments-outbound-broker/actuator/health/readiness"
  initialDelaySeconds: 30

#startupProbe details
startupProbeDetails:
  probePath: "/microservices/omni-domain-oms-shipments-outbound-broker/actuator/health/readiness"
  initialDelaySeconds: 30
  periodSeconds: 10
  failureThreshold: 30

#custom pod annotations
customPodAnnotations: |
  name: "{{ .Values.app.name }}"
  prometheus.io/port: "9464"
  instrumentation.opentelemetry.io/inject-java: "opentelemetry-operator/my-instrumentation"
  instrumentation.opentelemetry.io/container-names: "{{ .Values.app.name }}"
  
  #configMap BasePath
configBasePath: /additional-config

# ConfigMaps to be Created
configMap:
  application.yaml: |
    {{- .Files.Get "config/application.yaml" }}
  logback-spring.xml: |
    {{- .Files.Get "config/logback-spring.xml" }}

# Below values will be passed from helm template command during runtime under istio object example - istio.prodWeightage
#   prodWeightage: 100
#   canaryWeightage: 0
#   currentAppVersion: 
#   canaryAppVersion: 

istioHttp: |
  - match:
    - uri:
        prefix: /microservices/omni-domain-oms-shipments-outbound-broker/
    route:
    - destination:
        host: {{ .Values.app.name }}.{{ .Values.appData.nameSpace }}.svc.cluster.local
        port:
          number: {{ .Values.app.port }}
        subset: {{ regexReplaceAll "\\W+" .Values.istio.currentAppVersion "-" }}
      weight: {{ .Values.istio.prodWeightage }}
    - destination:
        host: {{ .Values.app.name }}.{{ .Values.appData.nameSpace }}.svc.cluster.local
        port:
          number: {{ .Values.app.port }}
        subset: {{ regexReplaceAll "\\W+" .Values.istio.canaryAppVersion "-" }}
      weight: {{ .Values.istio.canaryWeightage }}
