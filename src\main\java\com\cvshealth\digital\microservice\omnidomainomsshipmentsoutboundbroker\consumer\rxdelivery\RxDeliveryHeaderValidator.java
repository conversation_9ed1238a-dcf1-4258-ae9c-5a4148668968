package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.rxdelivery;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.HeaderConstants;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.HeaderValidator;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.ShipmentUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.List;
import java.util.Map;

public class RxDeliveryHeaderValidator implements HeaderValidator {

    private static final List<String> requiredHeaders = List.of(HeaderConstants.HEADER_EVENT,
                                                                HeaderConstants.HEADER_SOURCE,
                                                                HeaderConstants.HEADER_ACTION,
                                                                HeaderConstants.HEADER_LINE_OF_BUSINESS,
                                                                HeaderConstants.HEADER_ORDER_TYPE,
                                                                HeaderConstants.HEADER_ORDER_KEY,
                                                                HeaderConstants.HEADER_TIMESTAMP,
                                                                HeaderConstants.HEADER_CONVERSATION_ID);

    private final ShipmentUtil shipmentUtil;

    public RxDeliveryHeaderValidator(ShipmentUtil shipmentUtil) {
        this.shipmentUtil = shipmentUtil;
    }

    @Override
    public Map<String, String> validateAndGetHeaderMap(ConsumerRecord<String, byte[]> consumerRecord) {
        return shipmentUtil.validateAndGetHeaderMap(consumerRecord, requiredHeaders);
    }
}
