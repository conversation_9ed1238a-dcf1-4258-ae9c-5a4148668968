package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClient;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class RestClientConfigTest {

    private RestClientConfig restClientConfig;

    @BeforeEach
    void setUp() {
        restClientConfig = new RestClientConfig();
    }

    @Test
    void testRestClient_ShouldReturnConfiguredRestClient() {
        // Act
        RestClient restClient = restClientConfig.restClient();

        // Assert
        assertNotNull(restClient);
    }

    @Test
    void testRestClient_ShouldReturnDifferentInstancesOnMultipleCalls() {
        // Act
        RestClient restClient1 = restClientConfig.restClient();
        RestClient restClient2 = restClientConfig.restClient();

        // Assert
        assertNotNull(restClient1);
        assertNotNull(restClient2);
        assertNotSame(restClient1, restClient2);
    }

    @Test
    void testRestClient_ShouldUseHttpComponentsClientHttpRequestFactory() {
        // Act
        RestClient restClient = restClientConfig.restClient();

        // Assert
        assertNotNull(restClient);
        // We can't directly access the request factory from RestClient,
        // but we can verify that the RestClient was created successfully
        // which indicates the HttpComponentsClientHttpRequestFactory was used
    }

    @Test
    void testGetClientHttpRequestFactoryWithoutSsl_ShouldReturnConfiguredFactory() throws Exception {
        // Use reflection to access the private method for testing
        java.lang.reflect.Method method = RestClientConfig.class.getDeclaredMethod("getClientHttpRequestFactoryWithoutSsl");
        method.setAccessible(true);
        
        // Act
        HttpComponentsClientHttpRequestFactory factory = 
            (HttpComponentsClientHttpRequestFactory) method.invoke(restClientConfig);

        // Assert
        assertNotNull(factory);
        assertNotNull(factory.getHttpClient());
    }

    @Test
    void testGetClientHttpRequestFactoryWithoutSsl_ShouldConfigureTimeouts() throws Exception {
        // Use reflection to access the private method for testing
        java.lang.reflect.Method method = RestClientConfig.class.getDeclaredMethod("getClientHttpRequestFactoryWithoutSsl");
        method.setAccessible(true);
        
        // Act
        HttpComponentsClientHttpRequestFactory factory = 
            (HttpComponentsClientHttpRequestFactory) method.invoke(restClientConfig);

        // Assert
        assertNotNull(factory);
        // Verify that the factory was configured (we can't easily test specific timeout values
        // without accessing internal Apache HttpClient configuration)
    }

    @Test
    void testRestClientConfig_ShouldHaveCorrectAnnotations() {
        // Assert
        assertTrue(RestClientConfig.class.isAnnotationPresent(org.springframework.context.annotation.Configuration.class));
        assertTrue(RestClientConfig.class.isAnnotationPresent(org.springframework.context.annotation.Profile.class));
        
        org.springframework.context.annotation.Profile profileAnnotation = 
            RestClientConfig.class.getAnnotation(org.springframework.context.annotation.Profile.class);
        assertEquals("specialty", profileAnnotation.value()[0]);
    }

    @Test
    void testRestClientBean_ShouldHaveCorrectBeanAnnotation() throws Exception {
        // Act
        java.lang.reflect.Method restClientMethod = RestClientConfig.class.getMethod("restClient");
        
        // Assert
        assertTrue(restClientMethod.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
        
        org.springframework.context.annotation.Bean beanAnnotation = 
            restClientMethod.getAnnotation(org.springframework.context.annotation.Bean.class);
        assertEquals("restClient", beanAnnotation.name()[0]);
    }

    @Test
    void testRestClient_ShouldHandleMultipleInstantiations() {
        // Act & Assert
        for (int i = 0; i < 5; i++) {
            RestClient restClient = restClientConfig.restClient();
            assertNotNull(restClient);
        }
    }

    @Test
    void testRestClient_ShouldNotThrowExceptions() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            RestClient restClient = restClientConfig.restClient();
            assertNotNull(restClient);
        });
    }

    @Test
    void testRestClientConfig_ShouldBeInstantiable() {
        // Act
        RestClientConfig config = new RestClientConfig();

        // Assert
        assertNotNull(config);
    }

    @Test
    void testRestClientConfig_ShouldAllowMultipleInstances() {
        // Act
        RestClientConfig config1 = new RestClientConfig();
        RestClientConfig config2 = new RestClientConfig();

        // Assert
        assertNotNull(config1);
        assertNotNull(config2);
        assertNotSame(config1, config2);
    }
}
