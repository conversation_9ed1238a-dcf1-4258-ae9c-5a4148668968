package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util;

import com.ibm.mq.MQEnvironment;
import com.ibm.mq.MQException;
import com.ibm.mq.MQQueue;
import com.ibm.mq.MQQueueManager;
import com.ibm.mq.constants.CMQC;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@UtilityClass
@Slf4j
public class MQUtil {

    public boolean isQueueAvailable(String connName, String channel, String queueManager, String queueName) {
        MQQueueManager qmgr = null;
        try {

            String regex = "(.*)\\((\\d+)\\)";
            String host = null;
            String port = null;

            if (connName.matches(regex)) {

                host = connName.replaceAll(regex, "$1");
                port = connName.replaceAll(regex, "$2");
            }
            MQEnvironment.hostname = host;
            MQEnvironment.port = 30401;
            MQEnvironment.channel = channel;

            qmgr = new MQQueueManager(queueManager);

            // Access the queue with inquire mode
            MQQueue queueObject = qmgr.accessQueue(queueName, CMQC.MQOO_INQUIRE);

            log.info("Queue is available: " + queueName);
            return true;

        } catch (MQException e) {
            log.error("Queue not available: " + queueName + " Error: " + e.getMessage());
            return false;
        }
    }
}
