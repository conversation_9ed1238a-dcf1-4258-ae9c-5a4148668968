package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ShipmentUtil {

    public Map<String, String> validateAndGetHeaderMap(ConsumerRecord<String, byte[]> consumerRecord, List<String> requiredHeaders) {

        Map<String, String> headers = new HashMap<>();

        consumerRecord.headers().forEach(header -> {
            headers.put(header.key(), new String(header.value()));
        });

        List<String> missingHeaders = requiredHeaders.stream()
                .filter(requiredHeader -> !headers.containsKey(requiredHeader))
                .toList();

        if (!missingHeaders.isEmpty()) {
            throw new RuntimeException("Request missing required headers: " + missingHeaders);
        }

        if (log.isTraceEnabled()) {
            log.trace("Shipment received : {}", headers);
        }

        return headers;
    }

    public String getHeaderValue(Map<String, String> headers, String key) {
        if (headers.containsKey(key)) {
            return headers.get(key);
        } else {
            throw new RuntimeException("Header " + key + " not found");
        }
    }
}
