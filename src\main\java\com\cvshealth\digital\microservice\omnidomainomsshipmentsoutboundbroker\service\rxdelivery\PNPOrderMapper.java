package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery;

import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.framework.starter.service.encrypt.EncryptionService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.*;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.request.*;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.CVSCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.cvs.oms.common.util.ResourceHelper.resolve;



@Slf4j
public class PNPOrderMapper {

    private static final String VALUE_YES = "Y";
    private static final String VALUE_NO = "N";
    private static final String EMPTY = "";
    private static final String ADDRESS_TYPE_SHIP_TO = "SHIP_TO";
    private static final String ADDRESS_TYPE_MARK_FOR = "MARK_FOR";
    private static final String SHIPMENT_TYPE_BOPUS = "BOPUS";

    private final EncryptionService encryptionService;


    public PNPOrderMapper(EncryptionService encryptionService) {
        this.encryptionService = encryptionService;
    }

    public Root mapShipment(OMSFetchShipmentResponse omsFetchShipmentResponse) {

        Root root = new Root();
        mapStorePickupOrderRequest(omsFetchShipmentResponse, root);

        log.debug("Created PNP request: {}", root); // TODO - REMOVE THIS LATER!!

        return root;
    }

    private void mapStorePickupOrderRequest(OMSFetchShipmentResponse omsFetchShipmentResponse, Root root) {

        StorePickupOrderRequest orderRequest = new StorePickupOrderRequest();
        ShipmentDetails shipmentDetails = omsFetchShipmentResponse.getShipmentDetails();
        mapOrderInfo(shipmentDetails, orderRequest);
        mapShipmentCarrierInfo(omsFetchShipmentResponse, orderRequest);
        List<PaymentInfo> paymentInfoPNPList = getPaymentInfos(shipmentDetails);
        orderRequest.setPaymentInfo(paymentInfoPNPList);
        AdditionalOrderInfo additionalOrderInfo = new AdditionalOrderInfo();
        String paymentPreference = resolve(shipmentDetails.getShipmentExtn()::getPaymentPreference).orElse(EMPTY);
        if (paymentPreference.equals("PayAtStore")) {
            additionalOrderInfo.setPrePayOnline(VALUE_NO);
        } else {
            additionalOrderInfo.setPrePayOnline(VALUE_YES);
        }
        additionalOrderInfo.setPriority(resolve(()-> shipmentDetails.getShipmentExtn().getPlanPriority()).orElse(EMPTY));
        orderRequest.setAdditionalOrderInfo(additionalOrderInfo);
        mapOrderPickupInfo(omsFetchShipmentResponse,orderRequest);
        root.setStorePickupOrderRequest(orderRequest);
    }

    private List<PaymentInfo> getPaymentInfos(ShipmentDetails shipmentDetails) {
        List<ShipmentPaymentInfo> paymentInfoList = shipmentDetails.getShipmentExtn().getPaymentInfo();
        List<PaymentInfo> paymentInfoPNPList = new ArrayList<>();

        if (!Objects.isNull(paymentInfoList) && !paymentInfoList.isEmpty()) {
            paymentInfoList.forEach(paymentInfo -> {
                PaymentInfo paymentInfoPNP = new PaymentInfo();
                paymentInfoPNP.setCardNumber(resolve(paymentInfo::getCardNumber).orElse(EMPTY));
                paymentInfoPNP.setCardType(resolve(paymentInfo::getCardType).orElse(EMPTY));
                paymentInfoPNPList.add(paymentInfoPNP);
            });
        }

        return paymentInfoPNPList;
    }

    private void mapOrderInfo(ShipmentDetails shipmentDetails, StorePickupOrderRequest orderRequest) {

        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setRouteIdentifier("OMS");
        orderInfo.setEnterpriseCode("CVSHealth");

        if (null != shipmentDetails) {
            //TODO OTCHS
            List <ShipmentAddress> filteredAddress = shipmentDetails.getShipmentAddresses().stream()
                    .filter(shipmentAddress -> ADDRESS_TYPE_SHIP_TO.equalsIgnoreCase(shipmentAddress.getAddressType())
                    || "MARK_TO".equalsIgnoreCase(shipmentAddress.getAddressType()))
                    .toList();

            if (!filteredAddress.isEmpty()) {
                ShipmentAddress shipmentAddress = filteredAddress.getFirst();
                orderInfo.setChannelID(resolve(shipmentAddress::getPreferredContactPhone).orElse(EMPTY));
            }

            orderInfo.setChannelType(resolve(shipmentDetails::getOrderChannel).orElse(EMPTY));

            if (shipmentDetails.isCarepassOrder()) {
                orderInfo.setCarePassIndicator(VALUE_YES);
            } else {
                orderInfo.setCarePassIndicator(VALUE_NO);
            }

            if (shipmentDetails.getShipmentExtn() != null && shipmentDetails.getShipmentExtn().getPaymentInfo() != null) {
                List<ShipmentPaymentInfo> paymentInfo = resolve(() -> shipmentDetails.getShipmentExtn().getPaymentInfo()).orElse(new ArrayList<>());
                String cardType = !paymentInfo.isEmpty() ? resolve(() -> paymentInfo.getFirst().getCardType()).orElse(EMPTY) : EMPTY;
                if (cardType.equalsIgnoreCase("FLEX_CARD")) {
                    orderInfo.setMemberID(resolve(() -> shipmentDetails.getShipmentExtn().getOtchsOrderNo()).orElse(EMPTY));
                } else {
                    orderInfo.setMemberID(resolve(shipmentDetails::getCustomerProfileId).orElse(EMPTY));
                }
            }

            orderInfo.setMemberType(resolve(shipmentDetails::getCustomerProfileType).orElse(EMPTY));
            orderInfo.setDocType("0001");

            String entryPoint = resolve(()->shipmentDetails.getEntryType().equalsIgnoreCase("PNP") ? "PNP" : "DOTM").orElse("DOTM");
            orderInfo.setEntryPoint(entryPoint);

            String entryType;
            if (shipmentDetails.getEntryType().equals("otchsOrder")) {
                entryType = "OTCHS";
            } else if (shipmentDetails.getOrderType().equalsIgnoreCase("RX")) {
                entryType = "RXC";
            } else {
                entryType = shipmentDetails.getEntryType();
            }
            orderInfo.setEntryType(entryType);

            String deliveryMethod = getDeliveryAndShipMethod(shipmentDetails);
            orderInfo.setDeliveryMethod(deliveryMethod);

            if ("RxAttach".equals(shipmentDetails.getDeliveryCode())) {
                orderInfo.setDeliveryType("RxAttach");
            } else if ("SFSP".equalsIgnoreCase(shipmentDetails.getShipmentType()) && "SDD".equalsIgnoreCase(shipmentDetails.getDeliveryCode())) {
                orderInfo.setDeliveryType("SDD");
                orderInfo.setDeliveryMode("SDD");
            }

            orderInfo.setSalesOrderNumber(resolve(()->getSalesOrderNo(shipmentDetails)).orElse(EMPTY));
            orderInfo.setOrderId(resolve(shipmentDetails::getShipmentNo).orElse(EMPTY));
            stampDateAndTimeFields(shipmentDetails, orderInfo);
            orderInfo.setStoreNbr(resolve(shipmentDetails::getShipNode).orElse(EMPTY));
            orderInfo.setShipToStoreNbr(resolve(()->shipmentDetails.getShipmentItems().getFirst().getShipToStoreNo()).orElse(EMPTY));
            orderInfo.setStorePickUpLocation(resolve(shipmentDetails::getStorePickupLocation).orElse(EMPTY));
            orderInfo.setEcCardNumber(resolve(shipmentDetails::getExtraCareCardNo).orElse(EMPTY));

            if ("SFSP".equalsIgnoreCase(shipmentDetails.getShipmentType()) && "SDD".equalsIgnoreCase(shipmentDetails.getDeliveryCode())) {
                orderInfo.setOrderType("FS-DLVY");
            } else {
                orderInfo.setOrderType(resolve(shipmentDetails::getShipmentType).orElse(EMPTY));
            }

            ShipmentExtn shipmentExtn = shipmentDetails.getShipmentExtn();

            if (null != shipmentExtn) {
                orderInfo.setESig(resolve(shipmentExtn::getEsignature).orElse(EMPTY));
                orderInfo.setOrderSubTotal(resolve(shipmentExtn::getOrderSubTotal).orElse(0.00));
                orderInfo.setPaymentIndicator(resolve(shipmentExtn::getPaymentPreference).orElse(EMPTY));
            }

            mapCustomerInfo(shipmentDetails, orderInfo);
            mapItemInfo(shipmentDetails, orderInfo);
        }

        orderRequest.setOrderInfo(orderInfo);
    }

    private String getDeliveryAndShipMethod(ShipmentDetails shipmentDetails) {

        String deliveryMethod;
        String shipmentType = shipmentDetails.getShipmentType();
        String deliveryCode = shipmentDetails.getDeliveryCode();

        log.debug("Finding delivery method using shipmentType: {} and deliveryCode: {}", shipmentType, deliveryCode);

        if (shipmentType.equalsIgnoreCase("BOPUS") || deliveryCode.equalsIgnoreCase("SDD")) {
            deliveryMethod = "BOPUS_MOBILE";
        } else if (shipmentType.equalsIgnoreCase("SFSP")) {
            deliveryMethod = "SFS_MOBILE";
        } else if (shipmentType.equalsIgnoreCase("RX-SHP")) {
            deliveryMethod = deliveryCode;
        } else if (shipmentType.equalsIgnoreCase("RX-PICK")) {
            deliveryMethod = "ETW";
        } else if (shipmentType.equalsIgnoreCase("STS")) {
            deliveryMethod = "CF-STS";
        } else if (shipmentType.equalsIgnoreCase("ETW")) {
            deliveryMethod = "ETW";
        } else {
            deliveryMethod = EMPTY;
        }

        return deliveryMethod;
    }

    private void stampDateAndTimeFields(ShipmentDetails shipmentDetails, OrderInfo orderInfo) {

        resolve(shipmentDetails::getOrderDate).ifPresentOrElse(orderDate -> orderInfo.setOrderSubmittedDateTime(CVSCommonUtil.convertDateTime(orderDate)), () -> orderInfo.setOrderSubmittedDateTime(EMPTY));
        resolve(shipmentDetails::getStoreSlaDate).ifPresentOrElse(storeSla -> orderInfo.setOrderPromisedPickUpTime(CVSCommonUtil.convertDateTime(storeSla)), () -> orderInfo.setOrderPromisedPickUpTime(EMPTY));
        resolve(shipmentDetails::getCustomerSlaDate).ifPresentOrElse(customerSla -> orderInfo.setOrderPromisedDeliveryTime(CVSCommonUtil.convertDateTime(customerSla)),
                () -> orderInfo.setOrderPromisedDeliveryTime(EMPTY));
    }

    private String getSalesOrderNo(ShipmentDetails shipmentDetails) {
        if (shipmentDetails.getEntryType().equals("otchsOrder")) {
            return resolve(() -> shipmentDetails.getShipmentExtn().getOtchsOrderNo()).orElse(EMPTY);
        } else {
            return resolve(shipmentDetails::getOrderNo).orElse(EMPTY);
        }
    }

    private void mapCustomerInfo(ShipmentDetails shipmentDetails, OrderInfo orderInfo) {

        String addressType;

        log.debug("Checking existing shipmentType: {}", shipmentDetails.getShipmentType());

        if (shipmentDetails.getShipmentType().equals(SHIPMENT_TYPE_BOPUS)) {
            addressType = ADDRESS_TYPE_MARK_FOR;
        } else {
            addressType = ADDRESS_TYPE_SHIP_TO;
        }

        log.debug("Mapping customer info for address type: {}", addressType);

        if (Objects.isNull(shipmentDetails.getShipmentAddresses())) {
            log.warn("Shipment address list is empty for shipmentId: {}", shipmentDetails.getShipmentId());
            return;
        }

        List<ShipmentAddress> filteredAddress = shipmentDetails.getShipmentAddresses().stream()
                .filter(shipmentAddress -> addressType.equalsIgnoreCase(shipmentAddress.getAddressType()))
                .toList();

        log.debug("Filtered address size: {}", filteredAddress.size());

        if (filteredAddress.isEmpty()) {
            log.warn("No matching address found for address type: {}", addressType);
        }

        CustomerInfo customerInfo = getCustomerInfo(filteredAddress);
        orderInfo.setCustomerInfo(customerInfo);

    }

    private CustomerInfo getCustomerInfo(List<ShipmentAddress> filteredAddress) {

        log.debug("Will populate customer info..");

        CustomerInfo customerInfo = new CustomerInfo();
        if (!filteredAddress.isEmpty()) {
            ShipmentAddress shipmentAddress = filteredAddress.getFirst();
            customerInfo.setCustomerFirstName(resolve(shipmentAddress::getFirstName).orElse(EMPTY));
            customerInfo.setCustomerLastName(resolve(shipmentAddress::getLastName).orElse(EMPTY));
            customerInfo.setCustomerPhoneNumber(resolve(shipmentAddress::getDayPhone).orElse(EMPTY));
            customerInfo.setCustomerEmailAddress(resolve(shipmentAddress::getEmailId).orElse(EMPTY));
        }

        return customerInfo;
    }

    private void mapItemInfo(ShipmentDetails shipmentDetails, OrderInfo orderInfo) {

        ItemInfo itemInfo = new ItemInfo();
        List<Item> itemList = new ArrayList<>();
        List<ShipmentItem> shipmentItems =  shipmentDetails.getShipmentItems();

        if (!Objects.isNull(shipmentItems) && !shipmentItems.isEmpty()) {
            shipmentItems.forEach(shipmentItem -> {
                Item item = new Item();
                item.setItemDescription(resolve(shipmentItem::getItemDesc).orElse(EMPTY));
                item.setQuantityRequested(resolve(shipmentItem::getOrderedQuantity).orElse(0));
                item.setLineTotalWithoutTax(resolve(shipmentItem::getLineTotalWithoutTax).orElse(0.00));
                item.setUnitPrice(resolve(shipmentItem::getUnitPrice).orElse(0.00));
                item.setOriginalUnitPrice(resolve(shipmentItem::getOriginalUnitPrice).orElse(0.00));
                item.setItemId(resolve(shipmentItem::getItemId).orElse(EMPTY));
                item.setItemType(resolve(shipmentItem::getItemType).orElse(EMPTY));
                item.setFromstore(resolve(shipmentDetails::getShipNode).orElse(EMPTY));
                item.setTostore(resolve(shipmentItem::getShipToStoreNo).orElse(EMPTY));
                item.setRxNDC(resolve(shipmentItem::getRxNDC).orElse(EMPTY));
                if (resolve(shipmentItem::isHazmat).orElse(false)) {
                    item.setHazmatFlag(VALUE_YES);
                } else {
                    item.setHazmatFlag(VALUE_NO);
                }
                if ("RX-SHP".equals(shipmentDetails.getShipmentType()) && "SHP".equals(shipmentDetails.getDeliveryMethod())) {
                    item.setDeliveryMode("STH");
                } else if ("SFSP".equals(shipmentDetails.getShipmentType()) && "SFSP".equalsIgnoreCase(shipmentDetails.getDeliveryCode()) && "SHP".equals(shipmentDetails.getDeliveryMethod())) {
                    item.setDeliveryMode("SFS_MOBILE");
                } else if ("SFSP".equals(shipmentDetails.getShipmentType()) && "SDD".equalsIgnoreCase(shipmentDetails.getDeliveryCode()) && "SHP".equals(shipmentDetails.getDeliveryMethod())) {
                    item.setDeliveryMode("BOPUS_MOBILE");
                } else if ("BOPUS".equals(shipmentDetails.getShipmentType()) && "PICK".equals(shipmentDetails.getDeliveryMethod())) {
                    item.setDeliveryMode("BOPUS_MOBILE");
                } else if ("RX-PICK".equals(shipmentDetails.getShipmentType()) && "PICK".equals(shipmentDetails.getDeliveryMethod())) {
                    item.setDeliveryMode("STH");
                }

                item.setUnitOfMeasure("EACH");
                item.setUpcNumber(resolve(shipmentItem::getUpcNumber).orElse(EMPTY));
                item.setPrimeLineNumber(resolve(shipmentItem::getOrderLineNo).orElse(EMPTY));
                if (null != shipmentItem.getAlternateUpcList()) {
                    item.setAlternateUpcs(shipmentItem.getAlternateUpcList());
                }
                item.setFillNumber(resolve(shipmentItem::getFillNumber).orElse(EMPTY));
                item.setFillSeqNum(resolve(shipmentItem::getFillSeqNum).orElse(EMPTY));
                item.setFillVerNum(resolve(shipmentItem::getFillVerNum).orElse(EMPTY));
                item.setRxNumber(returnDecryptedValue(resolve(shipmentItem::getRxNumber).orElse(EMPTY)));
                if (resolve(shipmentItem::isSubstitutionAllowed).orElse(false) && null != shipmentItem.getSubstitutionItemList()) {
                    item.setSubstitutionFlag(VALUE_YES);
                    item.setSubstitutionItemList(shipmentItem.getSubstitutionItemList());
                } else {
                    item.setSubstitutionFlag(VALUE_NO);
                }
                mapShipmentItemExtnFields(item, shipmentItem);
                item.setUnitWeight(resolve(shipmentItem::getUnitWeight).orElse(EMPTY));
                item.setFsaFlag((resolve(shipmentItem::isFsaItem).orElse(false)) ? VALUE_YES : VALUE_NO);
                itemList.add(item);
            });
        }
        itemInfo.setItems(itemList);
        orderInfo.setItemInfo(itemInfo);
    }

    private void mapShipmentItemExtnFields(Item item, ShipmentItem shipmentItem) {
        ShipmentItemExtn shipmentItemExtn = shipmentItem.getShipmentItemExtn();
        if (null != shipmentItemExtn) {
            item.setPatientID(returnDecryptedValue(resolve(shipmentItemExtn::getPatientId).orElse(EMPTY)));
            item.setPatientType(resolve(shipmentItemExtn::getPatientType).orElse(EMPTY));
            item.setPatientFirstName(resolve(shipmentItemExtn::getPatientFirstName).orElse(EMPTY));
            item.setPatientLastName(resolve(shipmentItemExtn::getPatientLastName).orElse(EMPTY));
        }
    }

    private void mapShipmentCarrierInfo(OMSFetchShipmentResponse omsFetchShipmentResponse, StorePickupOrderRequest orderRequest) {

        ShipmentCarrierInfo shipmentCarrierInfo = new ShipmentCarrierInfo();

        ShipmentDetails shipmentDetails = omsFetchShipmentResponse.getShipmentDetails();
        if (null != shipmentDetails) {
            List<ShipmentContainer> shipmentContainerList = shipmentDetails.getShipmentContainers();
            if (!Objects.isNull(shipmentContainerList) && !shipmentContainerList.isEmpty()) {
                ShipmentContainer shipmentContainer = shipmentContainerList.getFirst();
                CarrierInfo carrierInfo = new CarrierInfo();
                carrierInfo.setPrimaryCourier(resolve(shipmentContainer::getCarrierServiceCode).orElse(EMPTY));
                carrierInfo.setTrackingId(resolve(shipmentContainer::getTrackingNo).orElse(EMPTY));
                shipmentCarrierInfo.setCarrierInfo(carrierInfo);
            } else {
                CarrierInfo carrierInfo = new CarrierInfo();
                if ("STS".equalsIgnoreCase(omsFetchShipmentResponse.getShipmentDetails().getShipmentType())) {
                    carrierInfo.setPrimaryCourier("UPS");
                } else {
                    carrierInfo.setPrimaryCourier(EMPTY);
                }
                carrierInfo.setTrackingId(EMPTY);
                shipmentCarrierInfo.setCarrierInfo(carrierInfo);
            }
            mapShipmentInfo(omsFetchShipmentResponse, shipmentCarrierInfo);
            if (null != shipmentDetails.getShipmentExtn()) {
                shipmentCarrierInfo.setShipHandlingInstruction(resolve(shipmentDetails.getShipmentExtn()::getShipHandlingInstruction).orElse(EMPTY));
                shipmentCarrierInfo.setShipMethodIndicator(resolve(shipmentDetails.getShipmentExtn()::getShipMethodIndicator).orElse(EMPTY));
            }
        }
        orderRequest.setShipmentCarrierInfo(shipmentCarrierInfo);
    }

    private void mapShipmentInfo(OMSFetchShipmentResponse omsFetchShipmentResponse, ShipmentCarrierInfo shipmentCarrierInfo) {

        ShipmentDetails shipmentDetails = omsFetchShipmentResponse.getShipmentDetails();
        List<ShipmentInfo> shipmentInfoList = new ArrayList<>();
        ShipmentInfo shipmentInfo = new ShipmentInfo();
        String shipMethod = getDeliveryAndShipMethod(shipmentDetails);
        shipmentInfo.setShipMethod(shipMethod);

        if (null != shipmentDetails.getShipmentExtn()) {
            shipmentInfo.setShipCharge(resolve(() -> shipmentDetails.getShipmentExtn().getShippingCharge()).orElse(0.00));
            shipmentInfo.setShipTax(resolve(() -> shipmentDetails.getShipmentExtn().getShippingTax()).orElse(0.00));
        }

        mapShipmentAddress(omsFetchShipmentResponse, shipmentInfo);
        shipmentInfoList.add(shipmentInfo);
        shipmentCarrierInfo.setShipmentInfo(shipmentInfoList);
    }

    private void mapShipmentAddress(OMSFetchShipmentResponse omsFetchShipmentResponse, ShipmentInfo shipmentInfo) {

        List<ShipAddress> shipAddressList = new ArrayList<>();
        List<ShipmentAddress> filteredAddress = omsFetchShipmentResponse.getShipmentDetails().getShipmentAddresses().stream()
                .filter(shipmentAddress -> ADDRESS_TYPE_SHIP_TO.equalsIgnoreCase(shipmentAddress.getAddressType()))
                .toList();

        if (!filteredAddress.isEmpty()) {
            ShipmentAddress shipmentAddress = filteredAddress.getFirst();
            ShipAddress shipAddress = new ShipAddress();
            shipAddress.setAddressLine1(resolve(shipmentAddress::getAddressLine1).orElse(EMPTY));
            shipAddress.setAddressLine2(resolve(shipmentAddress::getAddressLine2).orElse(EMPTY));
            shipAddress.setCity(resolve(shipmentAddress::getCity).orElse(EMPTY));
            shipAddress.setState(resolve(shipmentAddress::getState).orElse(EMPTY));
            shipAddress.setZipCode(resolve(shipmentAddress::getZipCode).orElse(EMPTY));
            shipAddress.setCountry(resolve(shipmentAddress::getCountry).orElse(EMPTY));
            shipAddressList.add(shipAddress);
        }

        shipmentInfo.setShipAddress(shipAddressList);

    }

    private void mapOrderPickupInfo(OMSFetchShipmentResponse omsFetchShipmentResponse, StorePickupOrderRequest orderRequest) {

        OrderPickUpInfo orderPickUpInfo = new OrderPickUpInfo();
        List<PickUpContact> pickUpContactList = new ArrayList<>();
        PickUpContact pickUpContact = new PickUpContact();

        if (omsFetchShipmentResponse.getShipmentDetails().getShipmentType().equals(SHIPMENT_TYPE_BOPUS)) {
            List<ShipmentAddress> filteredAddress = omsFetchShipmentResponse.getShipmentDetails().getShipmentAddresses().stream()
                    .filter(shipmentAddress -> ADDRESS_TYPE_MARK_FOR.equalsIgnoreCase(shipmentAddress.getAddressType()))
                    .toList();
            ShipmentAddress shipmentAddress = filteredAddress.getFirst();
            pickUpContact.setPickUpContFirstName(resolve(shipmentAddress::getFirstName).orElse(EMPTY));
            pickUpContact.setPickUpContLastName(resolve(shipmentAddress::getLastName).orElse(EMPTY));
            pickUpContact.setPickUpContEmail(resolve(shipmentAddress::getEmailId).orElse(EMPTY));
            pickUpContact.setPickUpContPhone(resolve(shipmentAddress::getDayPhone).orElse(EMPTY));
        }

        pickUpContactList.add(pickUpContact);
        orderPickUpInfo.setPickUpContacts(pickUpContactList);
        orderRequest.setOrderPickUpInfo(orderPickUpInfo);
    }

    private String returnDecryptedValue(String encryptedValue) {
        try {
            return StringUtils.hasText(encryptedValue) ? encryptionService.decrypt(encryptedValue) : encryptedValue;
        } catch (Exception e) {
            CvsLogger.error("Error while decrypting value, stamping whatever is received in input.", e);
            return encryptedValue;
        }
    }
}