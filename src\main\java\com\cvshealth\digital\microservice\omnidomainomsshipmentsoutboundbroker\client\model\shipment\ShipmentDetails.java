package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentDetails {

    private String shipmentId;
    private String shipmentNo;
    private String orderNo;
    private String customerProfileId;
    private String customerProfileType;
    private String shipNode;
    private String orderType;
    private String entryType;
    private String orderChannel;
    private String shipmentType;
    private String deliveryMethod;
    private String creationTimestamp;
    private String storeSlaDate;
    private String customerSlaDate;
    private String shipmentStatus;
    private String extraCareCardNo;
    private String deliveryCode;
    private List<ShipmentAddress> shipmentAddresses;
    private List<ShipmentItem> shipmentItems;
    private List<ShipmentContainer> shipmentContainers;
    private ShipmentExtn shipmentExtn;
    private boolean carepassOrder;
    private String storePickupLocation;
    private String shipNodeLocale;
    private String orderDate;
}
