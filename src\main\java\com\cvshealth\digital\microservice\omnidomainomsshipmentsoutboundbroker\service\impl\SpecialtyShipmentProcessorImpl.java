package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.impl;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty.ClientConfig;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.mapper.specialty.ExternalEntityMapper;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request.ExternalEntityCancelRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request.ExternalEntityCreateRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentProcessor;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.specialty.ClientService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;


@Slf4j
public class SpecialtyShipmentProcessorImpl implements ShipmentProcessor {

    private final ClientService clientService;
    private final ObjectMapper objectMapper;

    public SpecialtyShipmentProcessorImpl(ClientService clientService, ObjectMapper objectMapper) {
        this.clientService = clientService;
        this.objectMapper = objectMapper;
    }

    /*
     * Process specialty shipment given shipment details
     */
    @Override
    public void processShipment(Shipment shipment, Map<String, String> headers) {
        if(null != shipment) {
            log.info("Processing specialty shipment for shipment ID: " + shipment.getShipmentId() + " and shipment number: " + shipment.getShipmentNo());
            //340B shipment
            if (shipment.getShipNodeType().equalsIgnoreCase("B")) {
                process340BShipment(shipment, headers);
            }
            //TODO other specialty shipment types
        } else {
            log.error("Shipment details not found");
        }
    }

    /*
     * Cancel specialty shipment given shipment details
     */
    @Override
    public void cancelShipment(Shipment shipment, Map<String, String> headers) {
        if(null != shipment) {
            log.info("Cancel specialty shipment for shipment ID: " + shipment.getShipmentId() + " and shipment number: " + shipment.getShipmentNo());
            //340B shipment
            if (shipment.getShipNodeType().equalsIgnoreCase("B")) {
                cancel340BShipment(shipment, headers);
            }
            //TODO other specialty shipment types
        } else {
            log.error("Shipment details not found");
        }
    }

    /*
        * Process 340B shipment given shipment details
     */
    private void process340BShipment(Shipment shipment, Map<String, String> headers) {
        ExternalEntityMapper mapper = new ExternalEntityMapper();
        ExternalEntityCreateRequest request = mapper.mapToExternalEntityRequest(shipment);
        try {
            log.debug("ExternalEntityCreateRequest -> " + objectMapper.writeValueAsString(request));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        ClientConfig config = getClientConfig(shipment.getShipFromLocationCd());
        if(config == null){
            throw new RuntimeException("Client details not found for shipment ID: " + shipment.getShipmentId() + " and client: " + shipment.getShipFromLocationCd());
        }
        clientService.submitOrderToExternalEntity(shipment.getShipmentId(), headers, config, request);
    }

    /*
     * Cancel request to 340B shipment given shipment details ONLY when shipment status is ACKNOWLEDGED or PACKED
     */
    private void cancel340BShipment(Shipment shipment, Map<String, String> headers) {
        ExternalEntityCancelRequest request = new ExternalEntityCancelRequest();
        request.setShipmentId(shipment.getShipmentId());
        request.setShipmentNo(shipment.getShipmentNo());
        ClientConfig config = getClientConfig(shipment.getShipFromLocationCd());
        if(config == null){
            throw new RuntimeException("Client details not found for shipment ID: " + shipment.getShipmentId() + " and client: " + shipment.getShipFromLocationCd());
        }
        clientService.cancelOrderToExternalEntity(shipment.getShipmentId(), headers, config, request);
    }

    /*
     * Get client configuration based on client name
     */
    private ClientConfig getClientConfig(String clientName) {
        return clientService.getClientConfig(clientName);
    }

}
