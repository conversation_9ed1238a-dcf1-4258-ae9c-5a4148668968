package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.RestApiClient;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ShipmentDetailsServiceTest {

    @Mock
    private RestApiClient restApiClient;

    @InjectMocks
    private ShipmentDetailsService shipmentDetailsService;

    private Shipment mockShipment;

    @BeforeEach
    void setUp() {
        mockShipment = new Shipment();
        mockShipment.setShipmentId(12345L);
        mockShipment.setShipmentNo("SH-12345");
        mockShipment.setShipmentType("SPECIALTY");
        mockShipment.setOrderNo("ORD-12345");
        mockShipment.setShipNode("NODE-001");
    }

    @Test
    void getShipmentDetails_WithValidShipmentId_ShouldReturnShipment() {
        // Arrange
        String shipmentId = "12345";
        when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(mockShipment);

        // Act
        Shipment result = shipmentDetailsService.getShipmentDetails(shipmentId);

        // Assert
        assertNotNull(result);
        assertEquals(12345L, result.getShipmentId());
        assertEquals("SH-12345", result.getShipmentNo());
        assertEquals("SPECIALTY", result.getShipmentType());
        assertEquals("ORD-12345", result.getOrderNo());
        
        verify(restApiClient).getShipmentDetails(shipmentId);
    }

    @Test
    void getShipmentDetails_WithNullShipmentId_ShouldReturnNull() {
        // Arrange
        String shipmentId = null;
        when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(null);

        // Act
        Shipment result = shipmentDetailsService.getShipmentDetails(shipmentId);

        // Assert
        assertNull(result);
        verify(restApiClient).getShipmentDetails(shipmentId);
    }

    @Test
    void getShipmentDetails_WithEmptyShipmentId_ShouldReturnNull() {
        // Arrange
        String shipmentId = "";
        when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(null);

        // Act
        Shipment result = shipmentDetailsService.getShipmentDetails(shipmentId);

        // Assert
        assertNull(result);
        verify(restApiClient).getShipmentDetails(shipmentId);
    }

    @Test
    void getShipmentDetails_WithInvalidShipmentId_ShouldReturnNull() {
        // Arrange
        String shipmentId = "invalid-id";
        when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(null);

        // Act
        Shipment result = shipmentDetailsService.getShipmentDetails(shipmentId);

        // Assert
        assertNull(result);
        verify(restApiClient).getShipmentDetails(shipmentId);
    }

    @Test
    void getShipmentDetails_WithRestApiClientException_ShouldThrowException() {
        // Arrange
        String shipmentId = "12345";
        RuntimeException exception = new RuntimeException("API call failed");
        when(restApiClient.getShipmentDetails(shipmentId)).thenThrow(exception);

        // Act & Assert
        RuntimeException thrownException = assertThrows(RuntimeException.class, () -> {
            shipmentDetailsService.getShipmentDetails(shipmentId);
        });

        assertEquals("API call failed", thrownException.getMessage());
        verify(restApiClient).getShipmentDetails(shipmentId);
    }

    @Test
    void getShipmentDetails_WithDifferentShipmentIds_ShouldCallRestApiClientCorrectly() {
        // Arrange
        String[] shipmentIds = {"12345", "67890", "11111"};
        
        for (String shipmentId : shipmentIds) {
            Shipment shipment = new Shipment();
            shipment.setShipmentId(Long.parseLong(shipmentId));
            shipment.setShipmentNo("SH-" + shipmentId);
            
            when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(shipment);

            // Act
            Shipment result = shipmentDetailsService.getShipmentDetails(shipmentId);

            // Assert
            assertNotNull(result);
            assertEquals(Long.parseLong(shipmentId), result.getShipmentId());
            assertEquals("SH-" + shipmentId, result.getShipmentNo());
            
            verify(restApiClient).getShipmentDetails(shipmentId);
        }
    }

    @Test
    void getShipmentDetails_WithSpecialtyShipment_ShouldReturnCorrectData() {
        // Arrange
        String shipmentId = "12345";
        Shipment specialtyShipment = new Shipment();
        specialtyShipment.setShipmentId(12345L);
        specialtyShipment.setShipmentNo("SH-12345");
        specialtyShipment.setShipmentType("SPECIALTY");
        specialtyShipment.setOrderNo("ORD-12345");
        specialtyShipment.setShipNode("SPECIALTY-NODE");

        when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(specialtyShipment);

        // Act
        Shipment result = shipmentDetailsService.getShipmentDetails(shipmentId);

        // Assert
        assertNotNull(result);
        assertEquals(12345L, result.getShipmentId());
        assertEquals("SH-12345", result.getShipmentNo());
        assertEquals("SPECIALTY", result.getShipmentType());
        assertEquals("ORD-12345", result.getOrderNo());
        assertEquals("SPECIALTY-NODE", result.getShipNode());

        verify(restApiClient).getShipmentDetails(shipmentId);
    }

    @Test
    void getShipmentDetails_WithRxDeliveryShipment_ShouldReturnCorrectData() {
        // Arrange
        String shipmentId = "67890";
        Shipment rxDeliveryShipment = new Shipment();
        rxDeliveryShipment.setShipmentId(67890L);
        rxDeliveryShipment.setShipmentNo("SH-67890");
        rxDeliveryShipment.setShipmentType("RXDELIVERY");
        rxDeliveryShipment.setOrderNo("ORD-67890");
        rxDeliveryShipment.setShipNode("RXDELIVERY-NODE");

        when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(rxDeliveryShipment);

        // Act
        Shipment result = shipmentDetailsService.getShipmentDetails(shipmentId);

        // Assert
        assertNotNull(result);
        assertEquals(67890L, result.getShipmentId());
        assertEquals("SH-67890", result.getShipmentNo());
        assertEquals("RXDELIVERY", result.getShipmentType());
        assertEquals("ORD-67890", result.getOrderNo());
        assertEquals("RXDELIVERY-NODE", result.getShipNode());

        verify(restApiClient).getShipmentDetails(shipmentId);
    }

    @Test
    void getShipmentDetails_MultipleCallsWithSameId_ShouldCallRestApiClientEachTime() {
        // Arrange
        String shipmentId = "12345";
        when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(mockShipment);

        // Act
        Shipment result1 = shipmentDetailsService.getShipmentDetails(shipmentId);
        Shipment result2 = shipmentDetailsService.getShipmentDetails(shipmentId);
        Shipment result3 = shipmentDetailsService.getShipmentDetails(shipmentId);

        // Assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotNull(result3);
        
        // Verify that the REST API client is called each time (no caching)
        verify(restApiClient, times(3)).getShipmentDetails(shipmentId);
    }

    @Test
    void getShipmentDetails_WithLongShipmentId_ShouldHandleCorrectly() {
        // Arrange
        String shipmentId = "999999999999";
        Shipment largeIdShipment = new Shipment();
        largeIdShipment.setShipmentId(999999999999L);
        largeIdShipment.setShipmentNo("SH-999999999999");
        
        when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(largeIdShipment);

        // Act
        Shipment result = shipmentDetailsService.getShipmentDetails(shipmentId);

        // Assert
        assertNotNull(result);
        assertEquals(999999999999L, result.getShipmentId());
        assertEquals("SH-999999999999", result.getShipmentNo());
        
        verify(restApiClient).getShipmentDetails(shipmentId);
    }

    @Test
    void getShipmentDetails_VerifyMethodDelegation() {
        // Arrange
        String shipmentId = "12345";
        when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(mockShipment);

        // Act
        shipmentDetailsService.getShipmentDetails(shipmentId);

        // Assert
        // Verify that the service simply delegates to the REST API client
        verify(restApiClient, times(1)).getShipmentDetails(shipmentId);
        verifyNoMoreInteractions(restApiClient);
    }

    @Test
    void getShipmentDetails_WithWhitespaceShipmentId_ShouldPassThrough() {
        // Arrange
        String shipmentId = "  12345  ";
        when(restApiClient.getShipmentDetails(shipmentId)).thenReturn(mockShipment);

        // Act
        Shipment result = shipmentDetailsService.getShipmentDetails(shipmentId);

        // Assert
        assertNotNull(result);
        // Verify that the service passes the shipment ID as-is without trimming
        verify(restApiClient).getShipmentDetails("  12345  ");
    }
}
