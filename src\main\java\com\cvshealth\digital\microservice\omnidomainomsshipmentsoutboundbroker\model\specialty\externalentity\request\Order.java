package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.specialty.externalentity.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Order {
    private long shipmentId;
    private String shipmentNo;
    private String shipToFirstname;
    private String shipToMiddleInitial;
    private String shipToLastname;
    private String shipToAddress1;
    private String shipToAddress2;
    private String shipToCity;
    private String shipToState;
    private String shipToZip;
    private String shipViaDesc;
    private String shipPrefCarrier;
    private String csAreaCode;
    private String csPhone;
    private boolean coldPackFlag;
    private LocalDate dueDate;
    private String needsOnByIndicator;
    private String custLangCode;
    private String pickListComments;
    private String packerInstructions;
    private boolean additionalRxLabel;
    private String clientCode;
    private int patientCount;
    private int rxCount;
    private String feSiteAddress;
    private String feSiteCity;
    private String feSiteState;
    private String feSiteZip;
    private String feSitePhone;
    private String feSiteDEA;
    private int printPackPageCount;
    private String printPackFileName;
    private boolean orderDestCode;
    private String shipViaMethod;
    private String brandCode;
    private String preferredLanguage;
    private boolean dotFlag;
    private boolean uspsProhibit;
    private String bundleNote;
    private String siteID;
    private List<String> shippingOptions;
    private String carrierShipCd;
    private String carrierServiceCd;
    private Patient patient;
}
