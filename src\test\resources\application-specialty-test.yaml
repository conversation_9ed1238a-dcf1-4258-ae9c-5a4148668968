# --------------------
# Application Configuration
# Prefix: app.*
# --------------------

app:
  events:
    errorReprocessServiceEvent:
      enabled: true
      topic: digitalretail-fulfillment-cvs-oms-error-event-message-us
    shipmentInboundEvent:
      enabled: true
      topic: digitalomni-oms-specialty-shipment-status-inbound-broker-event-dev

# --------------------
# Spring Boot Framework / Service Configuration
# Prefix: service.*
# --------------------
service:
  context-path: /microservices/omni-domain-oms-shipments-outbound-broker
  encrypt:
    method: JASYPT
    jasypt:
      key: test-jasypt-key-for-testing-only
  logging:
    enabled: true
    #   Supported mode: CVSEVENT, LOGAPP
    mode: CVSEVENT, LOGAPP
    #   Supported destinations: CONSOLE, HTTP
    #   For HTTP, set log-app.url
    destination:
      - CONSOLE
    log-app:
      url: ""
    #   List of events to ignore while logging. Valid: ENTRY, EXIT, INFO, <PERSON><PERSON><PERSON>
    ignore-events:
    excluded-endpoints:
      - "/actuator*/**"
      - "/swagger*/**"
      - "/health*/**"
      - "/v3/api*/**"
      - "/metrics/**"
      - "/microservices/omni-domain-oms-shipments-outbound-broker/actuator*/**"
      - "/microservices/omni-domain-oms-shipments-outbound-broker/swagger*/**"
      - "/microservices/omni-domain-oms-shipments-outbound-broker/health*/**"
  # --------------------
  # Kafka client configuration
  # Prefix: service.kafka.*
  # For more details, refer: https://cvsdigital.atlassian.net/wiki/spaces/SBREF/pages/**********/digital-service-kafka
  # --------------------
  kafka:
    producers:
      default:
        enabled: false
        client-id: omni-domain-oms-shipment-outbound-broker-client
        bootstrap-servers: localhost:29092
        properties:
          security:
            protocol: SASL_PLAINTEXT
          ssl:
            endpoint:
              identification:
                algorithm: ""
          sasl:
            mechanism: PLAIN
            jaas:
              config: org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin123";
    consumers:
      outbound-event:
        enabled: true
        event-type: outbound-event
        consumer-class: com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.ShipmentConsumer
        client-id: omni-domain-oms-shipments-service
        group-id: digitalomni-oms-group-speciality-outbound-broker-listener-local
        bootstrap-servers: localhost:29092
        topics: digitalomni-oms-specialty-shipment-outbound-broker-event-dev
        commit-style: AT_LEAST_ONCE_HIGH_THROUGHPUT
        max-poll-interval: 5m
        max-poll-records: 50
        auto-offset-reset: latest
        consumer-concurrency: 1
        consumer-record-processor-concurrency: 1
        initial-delay: 5s
        properties:
          key.deserializer: org.apache.kafka.common.serialization.StringDeserializer
          value.deserializer: org.apache.kafka.common.serialization.ByteArrayDeserializer
          security.protocol: SASL_PLAINTEXT
          ssl.endpoint.identification.algorithm: ""
          sasl.mechanism: PLAIN
          sasl.jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin123";

shipment:
  url: http://localhost:21000/microservices/omni-domain-oms-shipments-service/v1/shipments

# --------------------
# REST Service configuration
# To use RestService, set rest.enabled=true and provide endpoints
# Prefix: rest.*
# --------------------
rest:
  enabled: false
  endpoint:
    service-name:
      operation-name:
        url: ""
        method: POST
        headers:
          accept: "application/json"
          content-type: "application/json"


# --------------------
# Server Configuration
# Prefix: server.*
# --------------------
server:
  port: 21001
  max-http-request-header-size: 80KB

# --------------------
# Spring Configuration including datasource
# Prefix: spring.*
# --------------------
spring:
  application:
    name: omni-domain-oms-shipments-outbound-broker
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  main:
    banner-mode: off
  jpa:
    open-in-view: false
  threads.virtual.enabled: true

resilience4j:
  retry:
    instances:
      submit-order-external-service-retry:
        max-attempts: 3
        wait-duration: 500ms
        retry-exceptions:
          - java.lang.RuntimeException
      cancel-order-external-service-retry:
        max-attempts: 3
        wait-duration: 500ms
        retry-exceptions:
          - java.lang.RuntimeException
  circuitbreaker:
    instances:
      submit-order-external-service-circuit-breaker:
        sliding-window-type: COUNT_BASED
        sliding-window-size: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 10s
        permitted-number-of-calls-in-half-open-state: 2
        automatic-transition-from-open-to-half-open-enabled: true
      cancel-order-external-service-circuit-breaker:
        sliding-window-type: COUNT_BASED
        sliding-window-size: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 10s
        permitted-number-of-calls-in-half-open-state: 2
        automatic-transition-from-open-to-half-open-enabled: true


# --------------------
# Api Information
# Prefix: info.*
# --------------------
info:
  app:
    name: omni-domain-oms-shipments-outbound-broker
    description: omni-domain-oms-shipment-outbound-broker spring boot microservice
    version: 1.0.0-SANPSHOT
    encoding: UTF-8
    java:
      source: 21
      target: 21

# --------------------
# Swagger configuration
# Prefix: springdoc.*
# --------------------
springdoc:
  packagesToScan: com.cvshealth
  pathsToMatch: /microservices/omni-domain-oms-shipment-outbound-broker/**
  swagger-ui:
    enabled: true
    path: /microservices/omni-domain-oms-shipment-outbound-broker/swagger/swagger-ui.html
  api-docs:
    enabled: true
    path: /microservices/omni-domain-oms-shipment-outbound-broker/swagger/api-docs

# --------------------
# Actuator monitoring
# Prefix: management.*
# --------------------
management:
  security:
    enabled: false
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true
    health:
      show-details: always
    httptrace:
      excluded-endpoints: /favicon.ico,/actuator/**,/metrics/**,/microservices/omni-domain-oms-shipment-service/actuator/**
  endpoints:
    web:
      base-path: /microservices/omni-domain-oms-shipment-outbound-broker/actuator
      exposure:
        include:
          - health
          - prometheus
  metrics:
    export:
      prometheus:
        enabled: true
    percentiles-histogram:
      http:
        server:
          requests: true
    distribution:
      sla:
        http:
          server:
            requests: 50ms

logging:
  level:
    org.springframework.web.client.RestClient: DEBUG
    io.github.resilience4j.retry: DEBUG
    com:
      cvshealth:
        digital:
          microservice:
            omnidomainomsshipmentsoutboundbroker: DEBUG

clients:
  configs:
    BOB:
      client-name: BOB
      token-url: https://dev-k33ekwgjhwvmch7u.us.auth0.com/oauth/token
      grant-type: client_credentials
      audience: https://systemA
      scopes:
        - read
        - update
      client-id: test-bob-client-id
      client-secret: test-bob-client-secret
      client-trust-store: test-bob-trust-store
      client-trust-store-password: test-bob-trust-store-password
      create-shipment-url: http://localhost:8888/v1/create
      cancel-shipment-url: http://localhost:8888/v1/cancel
    JOE:
      client-name: JOE
      token-url: https://dev-k33ekwgjhwvmch7u.us.auth0.com/oauth/token
      grant-type: client_credentials
      audience: https://systemB
      scopes:
        - read
      client-id: test-joe-client-id
      client-secret: test-joe-client-secret
      client-trust-store: test-joe-trust-store
      client-trust-store-password: test-joe-trust-store-password
      create-shipment-url: http://localhost:9999/v2/ordercreate
      cancel-shipment-url: http://localhost:9999/v2/ordercancel

