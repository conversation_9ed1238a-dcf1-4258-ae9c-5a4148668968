package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.pnp.response.Response;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PNPOrderSubmitFailedException extends RuntimeException {

    private String message;
    private String errorCode;
    private Response pnpResponse;

    public PNPOrderSubmitFailedException(String message, String errorCode) {
        this.errorCode = errorCode;
        this.message = message;
    }

    public PNPOrderSubmitFailedException(String message, String errorCode, Response pnpResponse) {
        this.errorCode = errorCode;
        this.message = message;
        this.pnpResponse = pnpResponse;
    }
}