package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

/**
 * omni-domain-oms-shipments-outbound-broker application tests.
 *
 * <AUTHOR> Shah
 */
@ActiveProfiles({"test", "specialty"})
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(properties = {
    "service.kafka.consumers.outbound-event.enabled=false",
    "service.kafka.producers.default.enabled=false",
    "spring.main.allow-bean-definition-overriding=true",
    "JASYPT_KEY=test-jasypt-key-for-testing-only"
})
class OmniDomainOmsShipmentsOutboundBrokerApplicationTests {

    @Test
    void contextLoads() {
        // This test verifies that the Spring application context loads successfully
        // with all the necessary beans and configurations
    }

}
