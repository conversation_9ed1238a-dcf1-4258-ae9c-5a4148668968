package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.impl;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.OMSFetchShipmentResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.ShipmentRequest;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentProcessor;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery.GetShipmentDetailsService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery.ShipmentOrderSubmitProcessor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;


@Slf4j
public class RxDeliveryShipmentProcessorImpl implements ShipmentProcessor {

    private final GetShipmentDetailsService getShipmentDetailsService;
    private final List<ShipmentOrderSubmitProcessor> shipmentOrderSubmitProcessors;

    public RxDeliveryShipmentProcessorImpl(GetShipmentDetailsService getShipmentDetailsService, List<ShipmentOrderSubmitProcessor> shipmentOrderSubmitProcessors) {
        this.getShipmentDetailsService = getShipmentDetailsService;
        this.shipmentOrderSubmitProcessors = shipmentOrderSubmitProcessors;
    }

    /*
     * Process RxDelivery shipment given shipment details
     */
    @Override
    public void processShipment(Shipment shipment, Map<String, String> headers) {

        log.debug("Processing RxDelivery shipment for shipment ID: {} and shipment number: {}", shipment.getShipmentId(), shipment.getShipmentNo());

        ShipmentRequest shipmentRequest = new ShipmentRequest();
        shipmentRequest.setShipmentId(String.valueOf(shipment.getShipmentId()));
        shipmentRequest.setFulfillmentChannel(shipment.getOrderChannel());

        OMSFetchShipmentResponse omsFetchShipmentResponse = getShipmentDetailsService.getShipmentDetails(shipmentRequest);

        log.debug("Fetched OMSFetchShipmentResponse successfully for shipment ID: {} and shipment number: {}", shipment.getShipmentId(), shipment.getShipmentNo());

        if (Objects.nonNull(omsFetchShipmentResponse)) {
            log.debug("Will now call processShipmentOrderEvent for shipment ID: {} and shipment number: {}", shipment.getShipmentId(), shipment.getShipmentNo());
            processShipmentOrderEvent(omsFetchShipmentResponse, shipment);
        } else {
            log.warn("OMSFetchShipmentResponse was null for shipment ID: {} and shipment number: {}", shipment.getShipmentId(), shipment.getShipmentNo());
        }
    }

    private void processShipmentOrderEvent(OMSFetchShipmentResponse omsFetchShipmentResponse, Shipment shipment) {

        log.info("Processing shipment order event for: {}", shipment.getShipmentId());

        if (omsFetchShipmentResponse.getShipmentDetails() == null) {
            throw new RuntimeException("Shipment details not found for shipment id: " + shipment.getShipmentId());
        }

        log.info("Processing message for Shipment type: {}", omsFetchShipmentResponse.getShipmentDetails().getShipmentType());

        ShipmentOrderSubmitProcessor shipmentOrderSubmitProcessor = shipmentOrderSubmitProcessors.stream()
                .filter(processor -> processor.handlesShipmentType(omsFetchShipmentResponse.getShipmentDetails().getShipmentType()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No processor found for shipment type: " + omsFetchShipmentResponse.getShipmentDetails().getShipmentType()));

        shipmentOrderSubmitProcessor.processShipmentOrderEvent(omsFetchShipmentResponse, shipment);
    }

    /*
     * Cancel RxDelivery shipment given shipment details
     */
    @Override
    public void cancelShipment(Shipment shipment, Map<String, String> headers) {
        // no-op for RxDelivery
    }

}
