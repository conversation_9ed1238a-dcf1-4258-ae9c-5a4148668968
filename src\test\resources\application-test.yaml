# Test configuration that overrides main application properties
# This file is loaded when the 'test' profile is active

service:
  encrypt:
    method: JASYPT
    jasypt:
      key: test-jasypt-key-for-testing-only
  kafka:
    consumers:
      outbound-event:
        enabled: false
    producers:
      default:
        enabled: false

# Override client configurations with test values
clients:
  configs:
    BOB:
      client-name: BOB
      token-url: https://dev-k33ekwgjhwvmch7u.us.auth0.com/oauth/token
      grant-type: client_credentials
      audience: https://systemA
      scopes:
        - read
        - update
      client-id: test-bob-client-id
      client-secret: test-bob-client-secret
      client-trust-store: test-bob-trust-store
      client-trust-store-password: test-bob-trust-store-password
      create-shipment-url: http://localhost:8888/v1/create
      cancel-shipment-url: http://localhost:8888/v1/cancel
    JOE:
      client-name: JOE
      token-url: https://dev-k33ekwgjhwvmch7u.us.auth0.com/oauth/token
      grant-type: client_credentials
      audience: https://systemB
      scopes:
        - read
      client-id: test-joe-client-id
      client-secret: test-joe-client-secret
      client-trust-store: test-joe-trust-store
      client-trust-store-password: test-joe-trust-store-password
      create-shipment-url: http://localhost:9999/v2/ordercreate
      cancel-shipment-url: http://localhost:9999/v2/ordercancel

# Disable web application for tests
spring:
  main:
    web-application-type: none
    allow-bean-definition-overriding: true

# Disable management endpoints for tests
management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true

# Minimal logging for tests
logging:
  level:
    root: WARN
    com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker: INFO
