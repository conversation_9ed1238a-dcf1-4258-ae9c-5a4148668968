package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentItemExtn {

    private String patientFirstName;
    private String patientLastName;
    private String rxNDC;
    private String patientId;
    private String patientType;
    private String cncOrderLineId;
    private String lineTotal;

}
