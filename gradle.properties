# -----------------------
# Gradle properties
# -----------------------
org.gradle.parallel=true
org.gradle.caching=true

# -----------------------
# Build properties
# -----------------------
artifactory_repo=cvsDigitalRepo
artifactory_url=https://cvsh.jfrog.io/artifactory/cvsdigital-maven
java_version=21

# -----------------------
# project properties
# -----------------------
group=com.cvshealth.digital.microservice
version=1.0.0
description=omni-domain-oms-shipments-outbound-broker description

# -----------------------
# digital spring boot framework properties. this has to go with latest version always
# -----------------------
digital_spring_boot_framework_version=3.1.1

# -----------------------
# Dependency versions
# -----------------------
junit_version=5.10.2
mockito_version=5.11.0
jbehave_version=5.2.0