package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty;

import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.io.HttpClientConnectionManager;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.io.SocketConfig;
import org.apache.hc.core5.util.Timeout;
import org.springframework.context.annotation.Profile;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Profile("specialty")
@Component
public class ExternalEntityRestClientCache {

    private final Map<String, RestClient> externalEntityRestClientCache = new ConcurrentHashMap<>();

    public RestClient getExternalEntityCachedRestClient(ClientConfig clientConfig) {
        return externalEntityRestClientCache.computeIfAbsent(clientConfig.getClientName(), id -> {
            try {
                return getExternalEntityRestClient(clientConfig);
            } catch(Exception ex) {
                throw new RuntimeException("Failed to create rest template for client " + clientConfig.getClientName(), ex);
            }
        });
    }

    public RestClient getExternalEntityRestClient(ClientConfig clientConfig) throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException, CertificateException, IOException {
        try {
            return RestClient.builder()
                    .requestFactory(getClientHttpRequestFactory(clientConfig))
                    .build();
        } catch (Exception ex) {
            CvsLogger.error("Error while creating rest client with SSL for client " + clientConfig.getClientName(), ex);
            throw ex;
        }
    }

    private HttpComponentsClientHttpRequestFactory getClientHttpRequestFactory(ClientConfig clientConfig) throws KeyStoreException, CertificateException, IOException, NoSuchAlgorithmException, KeyManagementException {

        SSLContext sslContext = SSLContext.getInstance("TLS");
        KeyStore trustStore = KeyStore.getInstance("jks");

        if(null != clientConfig.getClientTrustStore()) {
            byte[] decodedJKS = Base64.getDecoder().decode(clientConfig.getClientTrustStore().replaceAll("\\s", ""));
            trustStore.load(new ByteArrayInputStream(decodedJKS), clientConfig.getClientTrustStorePassword().toCharArray());
            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance("SunX509");
            trustManagerFactory.init(trustStore);
            sslContext.init(null, trustManagerFactory.getTrustManagers(), null);
        } else {
            sslContext.init(null, null, null);
        }
        SSLConnectionSocketFactory sslConFactory = new SSLConnectionSocketFactory(sslContext);

        Timeout timeout = Timeout.ofSeconds(30);

        HttpClientConnectionManager cm = PoolingHttpClientConnectionManagerBuilder.create()
                .setDefaultSocketConfig(SocketConfig.custom().setSoTimeout(timeout).build())
                .setDefaultConnectionConfig(ConnectionConfig.custom().setConnectTimeout(timeout).setConnectTimeout(timeout).build())
                .setSSLSocketFactory(sslConFactory)
                .build();

        CloseableHttpClient httpClient = HttpClients
                .custom()
                .setConnectionManager(cm)
                .setDefaultRequestConfig(RequestConfig.custom().setConnectionRequestTimeout(timeout).setResponseTimeout(timeout).build())
                .build();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        return requestFactory;
    }

}
