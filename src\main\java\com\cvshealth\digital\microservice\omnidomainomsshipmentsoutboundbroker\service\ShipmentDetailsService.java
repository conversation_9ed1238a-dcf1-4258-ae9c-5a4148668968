package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service;


import com.cvshealth.digital.framework.service.logging.service.CvsLogger;
import com.cvshealth.digital.framework.service.logging.service.LogServiceContext;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment.Shipment;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.RestApiClient;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.CVSConstants.ERROR_SERVERITY_CRITICAL;
import static com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.CVSConstants.TAG_ERROR_SERVERITY;

@Service
@RequiredArgsConstructor
public class ShipmentDetailsService {

    private final RestApiClient restClient;

    public Shipment getShipmentDetails(String shipmentId){
        try{
            return restClient.getShipmentDetails(shipmentId);
        } catch(Exception e){
            LogServiceContext.addTags(TAG_ERROR_SERVERITY, ERROR_SERVERITY_CRITICAL);
            CvsLogger.error("Error fetching shipment details", e);
            throw e;
        }
    }
}
