package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.specialty;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ClientConfigTest {

    private ClientConfig clientConfig;

    @BeforeEach
    void setUp() {
        clientConfig = new ClientConfig();
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        ClientConfig config = new ClientConfig();

        // Assert
        assertNotNull(config);
        assertFalse(config.isTestFlag(), "Test flag should default to false");
    }

    @Test
    void testAllArgsConstructor() {
        // Arrange
        List<String> scopes = Arrays.asList("read", "write");

        // Act
        ClientConfig config = new ClientConfig(
            "TEST_CLIENT",
            "https://auth.test.com/token",
            "client_credentials",
            "https://api.test.com",
            scopes,
            "test-client-id",
            "test-client-secret",
            "trust-store-data",
            "trust-store-password",
            "https://api.test.com/create",
            "https://api.test.com/cancel",
            true
        );

        // Assert
        assertEquals("TEST_CLIENT", config.getClientName());
        assertEquals("https://auth.test.com/token", config.getTokenUrl());
        assertEquals("client_credentials", config.getGrantType());
        assertEquals("https://api.test.com", config.getAudience());
        assertEquals(scopes, config.getScopes());
        assertEquals("test-client-id", config.getClientId());
        assertEquals("test-client-secret", config.getClientSecret());
        assertEquals("trust-store-data", config.getClientTrustStore());
        assertEquals("trust-store-password", config.getClientTrustStorePassword());
        assertEquals("https://api.test.com/create", config.getCreateShipmentUrl());
        assertEquals("https://api.test.com/cancel", config.getCancelShipmentUrl());
        assertTrue(config.isTestFlag());
    }

    @Test
    void testSettersAndGetters() {
        // Arrange
        List<String> scopes = Arrays.asList("read", "update");

        // Act
        clientConfig.setClientName("BOB");
        clientConfig.setTokenUrl("https://auth.example.com/oauth/token");
        clientConfig.setGrantType("client_credentials");
        clientConfig.setAudience("https://systemA");
        clientConfig.setScopes(scopes);
        clientConfig.setClientId("bob-client-id");
        clientConfig.setClientSecret("bob-client-secret");
        clientConfig.setClientTrustStore("base64-trust-store");
        clientConfig.setClientTrustStorePassword("password123");
        clientConfig.setCreateShipmentUrl("http://localhost:8888/v1/create");
        clientConfig.setCancelShipmentUrl("http://localhost:8888/v1/cancel");
        clientConfig.setTestFlag(true);

        // Assert
        assertEquals("BOB", clientConfig.getClientName());
        assertEquals("https://auth.example.com/oauth/token", clientConfig.getTokenUrl());
        assertEquals("client_credentials", clientConfig.getGrantType());
        assertEquals("https://systemA", clientConfig.getAudience());
        assertEquals(scopes, clientConfig.getScopes());
        assertEquals("bob-client-id", clientConfig.getClientId());
        assertEquals("bob-client-secret", clientConfig.getClientSecret());
        assertEquals("base64-trust-store", clientConfig.getClientTrustStore());
        assertEquals("password123", clientConfig.getClientTrustStorePassword());
        assertEquals("http://localhost:8888/v1/create", clientConfig.getCreateShipmentUrl());
        assertEquals("http://localhost:8888/v1/cancel", clientConfig.getCancelShipmentUrl());
        assertTrue(clientConfig.isTestFlag());
    }

    @Test
    void testScopesHandling() {
        // Arrange
        List<String> scopes = Arrays.asList("read", "write", "delete");

        // Act
        clientConfig.setScopes(scopes);

        // Assert
        assertEquals(3, clientConfig.getScopes().size());
        assertTrue(clientConfig.getScopes().contains("read"));
        assertTrue(clientConfig.getScopes().contains("write"));
        assertTrue(clientConfig.getScopes().contains("delete"));
    }

    @Test
    void testNullValues() {
        // Act
        clientConfig.setClientName(null);
        clientConfig.setTokenUrl(null);
        clientConfig.setScopes(null);

        // Assert
        assertNull(clientConfig.getClientName());
        assertNull(clientConfig.getTokenUrl());
        assertNull(clientConfig.getScopes());
    }

    @Test
    void testEmptyScopes() {
        // Arrange
        List<String> emptyScopes = Arrays.asList();

        // Act
        clientConfig.setScopes(emptyScopes);

        // Assert
        assertNotNull(clientConfig.getScopes());
        assertTrue(clientConfig.getScopes().isEmpty());
    }

    @Test
    void testTestFlagToggle() {
        // Arrange
        assertFalse(clientConfig.isTestFlag(), "Default should be false");

        // Act & Assert
        clientConfig.setTestFlag(true);
        assertTrue(clientConfig.isTestFlag());

        clientConfig.setTestFlag(false);
        assertFalse(clientConfig.isTestFlag());
    }

    @Test
    void testEqualsAndHashCode() {
        // Arrange
        ClientConfig config1 = new ClientConfig();
        config1.setClientName("TEST");
        config1.setTokenUrl("https://test.com");

        ClientConfig config2 = new ClientConfig();
        config2.setClientName("TEST");
        config2.setTokenUrl("https://test.com");

        ClientConfig config3 = new ClientConfig();
        config3.setClientName("DIFFERENT");
        config3.setTokenUrl("https://test.com");

        // Assert
        assertEquals(config1, config2);
        assertEquals(config1.hashCode(), config2.hashCode());
        assertNotEquals(config1, config3);
    }

    @Test
    void testToString() {
        // Arrange
        clientConfig.setClientName("TEST_CLIENT");
        clientConfig.setTokenUrl("https://auth.test.com");

        // Act
        String toString = clientConfig.toString();

        // Assert
        assertNotNull(toString);
        assertTrue(toString.contains("ClientConfig"));
        assertTrue(toString.contains("TEST_CLIENT"));
        assertTrue(toString.contains("https://auth.test.com"));
    }

    @Test
    void testCompleteConfiguration() {
        // Arrange & Act
        ClientConfig config = createCompleteClientConfig();

        // Assert
        assertNotNull(config.getClientName());
        assertNotNull(config.getTokenUrl());
        assertNotNull(config.getGrantType());
        assertNotNull(config.getAudience());
        assertNotNull(config.getScopes());
        assertNotNull(config.getClientId());
        assertNotNull(config.getClientSecret());
        assertNotNull(config.getCreateShipmentUrl());
        assertNotNull(config.getCancelShipmentUrl());
        
        // Verify URLs are valid format
        assertTrue(config.getTokenUrl().startsWith("https://"));
        assertTrue(config.getCreateShipmentUrl().startsWith("http"));
        assertTrue(config.getCancelShipmentUrl().startsWith("http"));
    }

    @Test
    void testConfigurationForDifferentClients() {
        // Arrange & Act
        ClientConfig bobConfig = createBobClientConfig();
        ClientConfig joeConfig = createJoeClientConfig();

        // Assert
        assertEquals("BOB", bobConfig.getClientName());
        assertEquals("JOE", joeConfig.getClientName());
        assertNotEquals(bobConfig.getAudience(), joeConfig.getAudience());
        assertNotEquals(bobConfig.getScopes(), joeConfig.getScopes());
    }

    /**
     * Helper method to create a complete client configuration
     */
    private ClientConfig createCompleteClientConfig() {
        ClientConfig config = new ClientConfig();
        config.setClientName("TEST_CLIENT");
        config.setTokenUrl("https://auth.test.com/oauth/token");
        config.setGrantType("client_credentials");
        config.setAudience("https://api.test.com");
        config.setScopes(Arrays.asList("read", "write"));
        config.setClientId("test-client-id");
        config.setClientSecret("test-client-secret");
        config.setClientTrustStore("base64-encoded-trust-store");
        config.setClientTrustStorePassword("trust-store-password");
        config.setCreateShipmentUrl("https://api.test.com/v1/create");
        config.setCancelShipmentUrl("https://api.test.com/v1/cancel");
        config.setTestFlag(false);
        return config;
    }

    /**
     * Helper method to create BOB client configuration
     */
    private ClientConfig createBobClientConfig() {
        ClientConfig config = new ClientConfig();
        config.setClientName("BOB");
        config.setAudience("https://systemA");
        config.setScopes(Arrays.asList("read", "update"));
        return config;
    }

    /**
     * Helper method to create JOE client configuration
     */
    private ClientConfig createJoeClientConfig() {
        ClientConfig config = new ClientConfig();
        config.setClientName("JOE");
        config.setAudience("https://systemB");
        config.setScopes(Arrays.asList("read"));
        return config;
    }
}
