package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.model.shipment;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.constants.ShipmentStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentStatusHs {
    private long shipmentStatusId;
    private long shipmentId;
    private long shipmentItemId;
    private long eventId;
    private String statusCd;
    private ShipmentStatus status;
    private int statusTypeCd;
    private Instant effectiveTs;
    private Instant expiredTs;

}
