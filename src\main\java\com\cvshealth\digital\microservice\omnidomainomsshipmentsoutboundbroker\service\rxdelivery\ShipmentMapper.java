package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery;


import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.OMSFetchShipmentResponse;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.ShipmentAddress;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.ShipmentDetails;

import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.model.shipment.ShipmentItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.StringWriter;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Component
@Slf4j
public class ShipmentMapper {


    public  String convertShipmentToDcMessage(OMSFetchShipmentResponse omsFetchShipmentResponse) throws Exception {
        log.info("Converting shipment order to DC message - Start");
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document doc = builder.newDocument();

        Element root = doc.createElement("CVS_TRANSACTION");

        doc.appendChild(root);

        addHeaderSection(doc, root, omsFetchShipmentResponse.getShipmentDetails());
        addBodySection(doc, root, omsFetchShipmentResponse.getShipmentDetails());

        log.info("Converting shipment order to DC message - End");
        return convertDocumentToString(doc);
    }


    private void addHeaderSection(Document doc, Element root, ShipmentDetails shipmentDetails) {
        log.info("Converting shipment order to DC message - Creating Header");
        Element header = doc.createElement("HEADER");
        root.appendChild(header);

        addElementWithText(doc, header, "MSG_ID", "WEB" +formatDate(shipmentDetails.getCreationTimestamp()) + shipmentDetails.getShipmentExtn().getOtchsOrderNo());
        addElementWithText(doc, header, "BUSINESS", "CVS.COM");
        addElementWithText(doc, header, "FROM", "CVS.COM");
        addElementWithText(doc, header, "TO", ("MADC".equals(shipmentDetails.getShipNode()) || "BVDC".equals(shipmentDetails.getShipNode())) ? "LUCAS" : "EXACTA");
        addElementWithText(doc, header, "TIMESTAMP", formatDate(shipmentDetails.getCreationTimestamp()));
        addElementWithText(doc, header, "MSG_TYPE", "ORDER_SUBMIT");
        addElementWithText(doc, header, "VERSION", "1.0");
    }


    private  void addBodySection(Document doc, Element root, ShipmentDetails shipmentDetails) {
        log.info("Converting shipment order to DC message - Adding body section");
        Element body = doc.createElement("BODY");
        root.appendChild(body);
        addMemberSection(doc, body, shipmentDetails);
        addOrderSection(doc, body, shipmentDetails);
    }


    private  void addMemberSection(Document doc, Element body, ShipmentDetails shipmentDetails) {
        log.info("Converting shipment order to DC message - Adding member section");
        Element member = doc.createElement("MEMBER");
        body.appendChild(member);


        Element memIds = doc.createElement("MEM_IDS");
        member.appendChild(memIds);

        Element memId = doc.createElement("MEM_ID");
        memId.setAttribute("TYPE", "CVS.COM");
        memId.setTextContent(shipmentDetails.getCustomerProfileId());
        memIds.appendChild(memId);


        Element name = doc.createElement("NAME");
        member.appendChild(name);
        ShipmentAddress billAddress =getShippingAddress(shipmentDetails,"BILL_TO");


        if(billAddress  != null) {
            addElementWithText(doc, name, "TITLE", "");
            addElementWithText(doc, name, "FIRST", billAddress.getFirstName());
            addElementWithText(doc, name, "MIDDLE", billAddress.getMiddleName());
            addElementWithText(doc, name, "LAST", billAddress.getLastName());


            Element address = doc.createElement("ADDRESS");
            member.appendChild(address);
           addressMapping(doc,address,billAddress);


            Element contact = doc.createElement("CONTACT");
            member.appendChild(contact);

            addElementWithText(doc, contact, "PHONE1", "");
            addElementWithText(doc, contact, "PHONE2", "");
            addElementWithText(doc, contact, "FAX", "");
            addElementWithText(doc, contact, "EMAIL", billAddress.getEmailId());
        }
    }

    private  ShipmentAddress getShippingAddress(ShipmentDetails shipmentDetails,String addressType){
        log.info("Converting shipment order to DC message - Address processing");


        if(!CollectionUtils.isEmpty(shipmentDetails.getShipmentAddresses())){
            for(ShipmentAddress address: shipmentDetails.getShipmentAddresses()){
                if(addressType.equalsIgnoreCase(address.getAddressType())){
                   return address;
                }
            }

        }
        return null;
    }
    private void addressMapping(Document doc, Element element,ShipmentAddress address){
        log.info("Converting shipment order to DC message - Address mapping");

        addElementWithText(doc, element, "ADDR1", address.getAddressLine1());
        addElementWithText(doc, element, "ADDR2", address.getAddressLine2());
        addElementWithText(doc, element, "CITY", address.getCity());
        addElementWithText(doc, element, "STATE", address.getState());
        addElementWithText(doc, element, "ZIP", address.getZipCode());
        addElementWithText(doc, element, "COUNTRY", address.getCountry());

    }


    private void addOrderSection(Document doc, Element body, ShipmentDetails shipmentDetails) {
        log.info("Converting shipment order to DC message - Adding order section");

        Element order = doc.createElement("ORDER");
        body.appendChild(order);


        Element orderIds = doc.createElement("ORDER_IDS");
        order.appendChild(orderIds);

        Element orderId = doc.createElement("ORDER_ID");
        orderId.setAttribute("TYPE", "CVS.COM");
        orderId.setTextContent(shipmentDetails.getShipmentNo());
        orderIds.appendChild(orderId);

        addElementWithText(doc, order, "SALES_ORDER_ID", shipmentDetails.getShipmentExtn().getOtchsOrderNo());
        addElementWithText(doc, order, "ORDER_DATE", formatDate(shipmentDetails.getOrderDate()));
        addElementWithText(doc, order, "PLANNED_DELIVERY_DATE", formatDate(shipmentDetails.getCustomerSlaDate()));
        addElementWithText(doc, order, "ORDER_SOURCE", "WEB");
        addElementWithText(doc, order, "ENTRY_TYPE", shipmentDetails.getEntryType());
        addElementWithText(doc,order,"OTCHS_PLAN_NAME",shipmentDetails.getShipmentExtn().getOtchsplan());
        addElementWithText(doc, order, "OTCHS_MEMBER_ID", shipmentDetails.getShipmentExtn().getOtchsMemberId());
        addElementWithText(doc,order,"TLOG_FILLER","");
        addElementWithText(doc, order, "DISCOUNT_CODE", "");
        addElementWithText(doc, order, "DISCOUNT_AMT", "0.0");
        addElementWithText(doc, order, "DISCOUNT_TEXT", "");
        addElementWithText(doc, order, "COUPON_AMT", "0.0");
        addElementWithText(doc, order, "EXTRABUCK_AMT", "0.0");
        addElementWithText(doc, order, "LOYALTY_NUM", "");

        addElementWithText(doc, order, "EXPECTED_SHIPMENT_DATE", "");
        Element contact = doc.createElement("CONTACT");
        order.appendChild(contact);

        addElementWithText(doc, contact, "PHONE1", "");
        addElementWithText(doc, contact, "PHONE2", shipmentDetails.getShipmentExtn().getOtchsPhoneNumber());
        addElementWithText(doc, contact, "FAX", "");
        addElementWithText(doc, contact, "EMAIL", "");
        Element credit = doc.createElement("CREDIT_CARDS");
        order.appendChild(credit);

        Element cc = doc.createElement("CC");
        credit.appendChild(cc);


        addElementWithText(doc, cc, "CC_TYPE", "");
        addElementWithText(doc, cc, "CC_NUM", "");
        addElementWithText(doc, cc, "CC_NAME", "");
        addElementWithText(doc, cc, "CC_EXP_CODE", "");
        addElementWithText(doc, cc, "CC_AUTH_CODE", "");




        Element deliveries = doc.createElement("DELIVERIES");
        order.appendChild(deliveries);


        Element delivery = doc.createElement("DELIVERY");
        delivery.setAttribute("ID", "1");
        delivery.setAttribute("TYPE", "SHIP_TO");

        deliveries.appendChild(delivery);


        Element deliveryName = doc.createElement("NAME");
        delivery.appendChild(deliveryName);
        ShipmentAddress deliverAddress = getShippingAddress(shipmentDetails,"SHIP_TO");
        if(deliverAddress != null) {
            addElementWithText(doc, deliveryName, "TITLE", "");
            addElementWithText(doc, deliveryName, "FIRST", deliverAddress.getFirstName());
            addElementWithText(doc, deliveryName, "MIDDLE", deliverAddress.getMiddleName());
            addElementWithText(doc, deliveryName, "LAST", deliverAddress.getLastName());



            Element deliveryAddress = doc.createElement("ADDRESS");
            delivery.appendChild(deliveryAddress);

            addressMapping(doc, deliveryAddress, deliverAddress);


        }

        Element shipMethods = doc.createElement("SHIP_METHODS");
        delivery.appendChild(shipMethods);

        Element shipMethod = doc.createElement("SHIP_METHOD");
        shipMethod.setAttribute("SEQ", "1");
        shipMethod.setTextContent("500");
        shipMethods.appendChild(shipMethod);
        addElementWithText(doc,delivery,"SHIP_AMT","0.0");
        addElementWithText(doc,delivery,"SHIP_TAX","0.0");
        addElementWithText(doc,delivery,"SUPPORT_PHONE_NUM","");
        addElementWithText(doc,delivery,"SHIP_COST_REMITTED","0.0");


        Element lineItems = doc.createElement("LINE_ITEMS");
        order.appendChild(lineItems);

        if (shipmentDetails.getShipmentItems() != null) {
            for (ShipmentItem item : shipmentDetails.getShipmentItems()) {
                Element lineItem = doc.createElement("LINE_ITEM");
                lineItem.setAttribute("TYPE", "OTC");
                lineItems.appendChild(lineItem);

                addElementWithText(doc, lineItem, "LINE_NUM", item.getOrderLineNo());
                addElementWithText(doc, lineItem, "DELIVERY_ID", Integer.toString(item.getShipmentItemNo()));
                addElementWithText(doc, lineItem, "FULFILLMENT", shipmentDetails.getShipNode());
                addElementWithText(doc, lineItem, "HAZ_MAT", item.isHazmat()? "Y" : "N");
                addElementWithText(doc,lineItem,"OTCHS_SKU_ID","");
                Element prodNum = doc.createElement("PROD_NUMS");
                lineItem.appendChild(prodNum);
                addElementWithText(doc,prodNum, "PROD_NUM", item.getItemId());
                addElementWithText(doc, lineItem, "QTY", Integer.toString(item.getOrderedQuantity()));
                addElementWithText(doc, lineItem, "LIST_PRICE", String.valueOf(item.getLineTotalWithoutTax()));
                addElementWithText(doc, lineItem, "UNIT_PRICE", String.valueOf(item.getUnitPrice()));
                addElementWithText(doc,lineItem,"TPR","N");
                addElementWithText(doc, lineItem, "TAXABLE_AMT", String.valueOf(Double.valueOf(item.getLineTotalWithoutTax())));
                addElementWithText(doc, lineItem, "TAX_AMT",String.valueOf(Double.valueOf(item.getLineTax())));
                addElementWithText(doc, lineItem, "TOTAL_AMT", String.valueOf(Double.valueOf(item.getLineTotalWithoutTax()+item.getLineTax())));
                addElementWithText(doc,lineItem,"TAX_RATE","");
                addElementWithText(doc,lineItem,"TAX_CODE","0000");
                addElementWithText(doc,lineItem,"COPAY_AMT","0.0");
                addElementWithText(doc, lineItem, "DISCOUNT_AMT", String.valueOf(Double.valueOf((item.getLineTotalWithoutTax())  / (item.getOrderedQuantity()))));
                addElementWithText(doc,lineItem,"DISCOUNT_PERCENT","");
                addElementWithText(doc,lineItem,"DISCOUNT_CODE","");
                addElementWithText(doc,lineItem,"DISCOUNT_TEXT","");
                addElementWithText(doc,lineItem,"PROMO_CODE","");
                addElementWithText(doc,lineItem,"ORDER_TYPE_CODE","*");
                addElementWithText(doc,lineItem,"AVO_NUM","0000");
                addElementWithText(doc, lineItem, "SELLING_PRICE", String.valueOf(Double.valueOf(item.getUnitPrice())));
                addElementWithText(doc, lineItem, "TOTAL_SELLING_PRICE", String.valueOf(Double.valueOf(item.getLineTotalWithoutTax()+item.getLineTax())));
                addElementWithText(doc,lineItem,"LINE_ITEM_STATUS","N");
                addElementWithText(doc,lineItem,"TLOG_FILLER","");
                addElementWithText(doc, lineItem, "EXPECTED_SHIPMENT_DATE", "");


            }
            addElementWithText(doc, order, "EXTN_RETURN_BARCODE", "");
            addElementWithText(doc, order, "SHIP_INSTRUCTION_TYPE",shipmentDetails.getShipmentExtn().getOtchsShipInstructionType());


        }
    }


    private void addElementWithText(Document doc, Element parent, String tagName, String textContent) {
        Element element = doc.createElement(tagName);
        element.setTextContent(textContent != null ? textContent : "");
        parent.appendChild(element);
    }


    private String convertDocumentToString(Document doc) throws Exception {
        log.info("Converting shipment order to DC message - Convert Document to string");

        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
        transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(doc), new StreamResult(writer));
        return writer.toString();
    }


    private String formatDate(String isoDate) {
        return isoDate != null ? ZonedDateTime.parse(isoDate)
                .withZoneSameInstant(ZoneId.of("America/New_York"))
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) : "";
    }


}