package com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.config.rxdelivery;

import com.cvshealth.digital.framework.starter.service.encrypt.EncryptionService;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.client.rxdelivery.RxDeliveryRestApiClient;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.HeaderValidator;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.consumer.rxdelivery.RxDeliveryHeaderValidator;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.ShipmentProcessor;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.impl.RxDeliveryShipmentProcessorImpl;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.service.rxdelivery.*;
import com.cvshealth.digital.microservice.omnidomainomsshipmentsoutboundbroker.util.ShipmentUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.web.client.RestClient;

import java.util.List;

@Configuration
@Profile("rxdelivery")
public class RxDeliveryConfig {

    @Bean
    public ShipmentProcessor getShipmentProcessor(GetShipmentDetailsService getShipmentDetailsService,
                                                  List<ShipmentOrderSubmitProcessor> shipmentOrderSubmitProcessor) {
        return new RxDeliveryShipmentProcessorImpl(getShipmentDetailsService, shipmentOrderSubmitProcessor);
    }

    @Bean
    public GetShipmentDetailsService getShipmentDetailsService(RxDeliveryRestApiClient restApiClient) {
        return new GetShipmentDetailsService(restApiClient);
    }

    @Bean
    public DcOrderSubmitProcessorImpl dcOrderSubmitProcessorImpl(ShipmentService shipmentService) {
        return new DcOrderSubmitProcessorImpl(shipmentService);
    }

    @Bean
    public PnpOrderSubmitProcessorImpl pnpOrderSubmitProcessorImpl(PNPShipmentOutboundBrokerService pnpShipmentOutboundBrokerService) {
        return new PnpOrderSubmitProcessorImpl(pnpShipmentOutboundBrokerService);
    }

    @Bean
    public ShipmentService shipmentService(ShipmentMapper mapper, ErrorReprocessorService errorReprocessorService, JmsTemplate jmsTemplate) {
        return new ShipmentService(mapper, errorReprocessorService, jmsTemplate);
    }

    @Bean
    public ErrorReprocessorService errorReprocessorService() {
        return new ErrorReprocessorService();
    }

    @Bean
    public PNPOrderMapper pnpOrderMapper(EncryptionService encryptionService) {
        return new PNPOrderMapper(encryptionService);
    }

    @Bean
    public OMSMapper omsMapper() {
        return new OMSMapper();
    }

    @Bean
    public PNPShipmentOutboundBrokerService pnpShipmentOutboundBrokerService(RxDeliveryRestApiClient restApiClient, PNPOrderMapper pnpOrderMapper,
                                                                      OMSMapper omsMapper, ErrorReprocessorService errorReprocessorService) {

        return new PNPShipmentOutboundBrokerService(restApiClient, pnpOrderMapper, omsMapper, errorReprocessorService);
    }

    @Bean
    public RxDeliveryRestApiClient rxDeliveryRestApiClient(@Qualifier("restClient") RestClient restClient, @Qualifier("restClientWithSsl") RestClient restClientWithSsl) {
        return new RxDeliveryRestApiClient(restClient, restClientWithSsl);
    }

    @Bean
    public HeaderValidator headerValidator(ShipmentUtil shipmentUtil) {
        return new RxDeliveryHeaderValidator(shipmentUtil);
    }

}
